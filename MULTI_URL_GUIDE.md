# 多URL配置指南

本系统支持同时监控多个招标网站，提供了三种不同的配置方案。

## 方案一：简单多URL（推荐）

所有网站使用相同的解析配置，适合同一个网站的不同栏目。

### 配置方法

1. **编辑配置文件**：
```env
# 使用逗号分隔多个URL
TARGET_URLS=https://ec.westmining.com/cms/channel/1ywgg1/index.htm,https://ec.westmining.com/cms/channel/1ywgg2/index.htm,https://ec.westmining.com/cms/channel/1ywgg3/index.htm

# 企业微信配置
CORP_ID=your_corp_id_here
CORP_SECRET=your_corp_secret_here
AGENT_ID=your_agent_id_here

# 其他配置
REQUEST_DELAY=3
SCHEDULE_TIME=09:00
```

2. **运行命令**：
```bash
# 测试
python main_multi_url.py --test --mode simple

# 执行一次
python main_multi_url.py --once --mode simple

# 启动定时任务
python main_multi_url.py --mode simple
```

### 特点
- ✅ 配置简单
- ✅ 适合同一网站的多个栏目
- ✅ 自动去重
- ✅ 按日期排序

## 方案二：高级多配置

不同网站使用不同的解析配置，适合完全不同的网站结构。

### 配置方法

1. **编辑配置文件**：
```env
# 多个不同类型的网站
TARGET_URLS=https://ec.westmining.com/cms/channel/1ywgg1/index.htm,http://www.ccgp.gov.cn/cggg/zygg/,https://province-site.gov.cn/bidding/

# 企业微信配置
CORP_ID=your_corp_id_here
CORP_SECRET=your_corp_secret_here
AGENT_ID=your_agent_id_here
```

2. **自定义URL配置映射**（可选）：

编辑 `src/multi_site_configs.py` 中的 `URL_CONFIG_MAPPING`：
```python
URL_CONFIG_MAPPING = {
    'https://ec.westmining.com/cms/channel/1ywgg1/index.htm': 'westmining_bidding',
    'https://ec.westmining.com/cms/channel/1ywgg2/index.htm': 'westmining_notice',
    'http://www.ccgp.gov.cn/cggg/zygg/': 'ccgp_gov',
    'https://province-site.gov.cn/bidding/': 'province_center',
}
```

3. **运行命令**：
```bash
# 测试
python main_multi_url.py --test --mode advanced

# 执行一次
python main_multi_url.py --once --mode advanced

# 启动定时任务
python main_multi_url.py --mode advanced
```

### 特点
- ✅ 支持完全不同的网站
- ✅ 自动配置检测
- ✅ 手动配置映射
- ✅ 详细的测试报告

## 方案三：多实例部署

为每个网站运行独立的实例，适合需要不同推送时间或接收人的场景。

### 配置方法

1. **创建多个配置文件**：

`config1.env`：
```env
TARGET_URL=https://ec.westmining.com/cms/channel/1ywgg1/index.htm
CORP_ID=your_corp_id_here
CORP_SECRET=your_corp_secret_here
AGENT_ID=your_agent_id_here
TO_USER=user1
SCHEDULE_TIME=09:00
```

`config2.env`：
```env
TARGET_URL=https://ec.westmining.com/cms/channel/1ywgg2/index.htm
CORP_ID=your_corp_id_here
CORP_SECRET=your_corp_secret_here
AGENT_ID=your_agent_id_here
TO_USER=user2
SCHEDULE_TIME=10:00
```

2. **运行多个实例**：
```bash
# 实例1
cp config1.env .env
python main.py --config mining_site &

# 实例2
cp config2.env .env
python main.py --config mining_site &
```

### 特点
- ✅ 完全独立的配置
- ✅ 不同的推送时间
- ✅ 不同的接收人
- ❌ 资源占用较多

## 配置示例

### 西矿集团多栏目配置

```env
# 监控招标公告、中标公告、中标结果三个栏目
TARGET_URLS=https://ec.westmining.com/cms/channel/1ywgg1/index.htm,https://ec.westmining.com/cms/channel/1ywgg2/index.htm,https://ec.westmining.com/cms/channel/1ywgg3/index.htm

CORP_ID=your_corp_id_here
CORP_SECRET=your_corp_secret_here
AGENT_ID=your_agent_id_here
REQUEST_DELAY=3
SCHEDULE_TIME=09:00
```

### 多个不同网站配置

```env
# 监控西矿集团、政府采购网、省级交易中心
TARGET_URLS=https://ec.westmining.com/cms/channel/1ywgg1/index.htm,http://www.ccgp.gov.cn/cggg/zygg/,https://province.gov.cn/bidding/

CORP_ID=your_corp_id_here
CORP_SECRET=your_corp_secret_here
AGENT_ID=your_agent_id_here
REQUEST_DELAY=5
SCHEDULE_TIME=09:00
```

## 添加新网站支持

### 1. 分析网站结构

使用调试脚本分析新网站：
```python
from src.scraper import WebScraper
from bs4 import BeautifulSoup

url = "https://new-site.com/bidding/"
scraper = WebScraper()
html = scraper.fetch_page(url)
soup = BeautifulSoup(html, 'lxml')

# 查找招标信息容器
items = soup.select('你的选择器')
print(f"找到 {len(items)} 条记录")
```

### 2. 创建配置

在 `src/multi_site_configs.py` 中添加：
```python
'new_site': {
    'item_selector': '.bidding-item',
    'title_selector': '.title a',
    'date_selector': '.date',
    'base_url': 'https://new-site.com'
}
```

### 3. 添加URL映射

```python
URL_CONFIG_MAPPING = {
    'https://new-site.com/bidding/': 'new_site',
    # ... 其他映射
}
```

## 性能优化建议

### 1. 合理设置延时
```env
# 网站数量多时增加延时
REQUEST_DELAY=5
```

### 2. 错峰执行
```env
# 不同实例使用不同时间
SCHEDULE_TIME=09:00  # 实例1
SCHEDULE_TIME=09:30  # 实例2
SCHEDULE_TIME=10:00  # 实例3
```

### 3. 监控日志
```bash
# 查看日志
tail -f logs/app.log

# 按日期查看
grep "2025-07-03" logs/app.log
```

## 故障排除

### 1. 某个网站抓取失败
- 检查网站是否可访问
- 调整该网站的延时设置
- 检查选择器配置是否正确

### 2. 消息过长被截断
- 系统会自动截断过长消息
- 可以调整 `MessageFormatter` 中的长度限制
- 考虑分批发送

### 3. 重复信息
- 系统会自动去重
- 基于标题和链接进行去重
- 如需调整去重逻辑，修改 `_remove_duplicates` 方法

## 最佳实践

1. **从简单开始**：先使用方案一测试，确认无误后再考虑高级配置
2. **逐步添加**：一次添加一个网站，确认正常后再添加下一个
3. **定期检查**：网站结构可能变化，定期检查解析是否正常
4. **备份配置**：重要配置文件要备份
5. **监控日志**：定期查看日志，及时发现问题

选择适合您需求的方案，开始使用多URL招标信息监控系统！
