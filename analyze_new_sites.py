#!/usr/bin/env python3
"""
分析新网站的HTML结构
"""
from src.scraper import WebScraper
from bs4 import BeautifulSoup

def analyze_site(url, site_name):
    """分析网站结构"""
    print(f"\n🔍 分析网站: {site_name}")
    print(f"URL: {url}")
    print("=" * 60)
    
    scraper = WebScraper()
    html_content = scraper.fetch_page(url)
    
    if not html_content:
        print("❌ 网页抓取失败")
        return None
    
    print(f"✅ 网页抓取成功，内容长度: {len(html_content)}")
    
    soup = BeautifulSoup(html_content, 'lxml')
    
    # 保存HTML到文件用于分析
    filename = f"debug_{site_name.lower().replace(' ', '_')}.html"
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    print(f"💾 HTML已保存到: {filename}")
    
    # 查找可能的招标信息容器
    print(f"\n🔍 查找可能的招标信息容器...")
    
    selectors_to_test = [
        'li',                    # li元素
        'tr',                    # 表格行
        '.list-item',           # 列表项
        '.item',                # 项目
        '.news-item',           # 新闻项
        '.bidding-item',        # 招标项
        'ul li',                # ul下的li
        'table tr',             # 表格行
        '.content li',          # 内容区域的li
        '.main li',             # 主区域的li
        'div[class*="list"]',   # 包含list的div
        'div[class*="item"]',   # 包含item的div
    ]
    
    best_selectors = []
    
    for selector in selectors_to_test:
        try:
            elements = soup.select(selector)
            count = len(elements)
            
            if 5 <= count <= 50:  # 合理数量的元素
                print(f"  ✅ {selector}: 找到 {count} 个元素")
                
                # 检查前几个元素是否包含链接
                has_links = 0
                for elem in elements[:5]:
                    if elem.select_one('a'):
                        has_links += 1
                
                if has_links >= 3:  # 大部分元素都有链接
                    best_selectors.append((selector, count, has_links))
                    print(f"    📋 前5个元素中有 {has_links} 个包含链接")
            elif count > 0:
                print(f"  ⚠️ {selector}: 找到 {count} 个元素 (数量不合理)")
        except Exception as e:
            continue
    
    # 显示最佳选择器的详细信息
    if best_selectors:
        print(f"\n📋 推荐的选择器:")
        best_selectors.sort(key=lambda x: (x[2], -abs(x[1]-15)), reverse=True)  # 按链接数和合理数量排序
        
        for i, (selector, count, links) in enumerate(best_selectors[:3], 1):
            print(f"\n{i}. 选择器: {selector}")
            print(f"   元素数量: {count}")
            print(f"   包含链接: {links}/5")
            
            # 显示前3个元素的详细信息
            elements = soup.select(selector)
            print(f"   前3个元素详情:")
            
            for j, elem in enumerate(elements[:3], 1):
                print(f"     [{j}]:")
                
                # 查找链接
                link = elem.select_one('a')
                if link:
                    title = link.get('title', link.get_text(strip=True)[:50])
                    href = link.get('href', '')
                    print(f"       标题: {title}")
                    print(f"       链接: {href}")
                else:
                    text = elem.get_text(strip=True)[:100]
                    print(f"       文本: {text}")
                
                # 查找可能的日期
                date_patterns = ['时间', '日期', 'time', 'date', '发布']
                for pattern in date_patterns:
                    date_elem = elem.select_one(f'*[class*="{pattern}"], *[id*="{pattern}"]')
                    if date_elem:
                        print(f"       日期候选: {date_elem.get_text(strip=True)}")
                        break
                
                # 查找所有可能包含日期的元素
                all_text = elem.get_text()
                import re
                dates = re.findall(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}', all_text)
                if dates:
                    print(f"       发现日期: {dates}")
    else:
        print(f"❌ 未找到合适的选择器")
    
    scraper.close()
    return best_selectors

def main():
    """主函数"""
    print("🚀 开始分析新网站结构")
    
    # 分析两个网站
    sites = [
        ("http://mn.southmn.com/list/?368_1.html", "南蒙古网站"),
        ("https://www.westmininggroup.com/tzgg/zbgg1/", "西矿集团官网")
    ]
    
    results = {}
    
    for url, name in sites:
        try:
            result = analyze_site(url, name)
            results[name] = result
        except Exception as e:
            print(f"❌ 分析 {name} 时发生错误: {e}")
            results[name] = None
    
    # 总结
    print(f"\n" + "="*60)
    print(f"📊 分析总结")
    print(f"="*60)
    
    for name, result in results.items():
        if result:
            print(f"✅ {name}: 找到 {len(result)} 个可用选择器")
        else:
            print(f"❌ {name}: 分析失败")
    
    print(f"\n💡 下一步:")
    print(f"1. 查看生成的HTML文件了解网站结构")
    print(f"2. 根据分析结果创建配置")
    print(f"3. 测试配置是否正确")

if __name__ == '__main__':
    main()
