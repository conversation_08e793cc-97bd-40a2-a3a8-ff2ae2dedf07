"""
消息格式化模块
"""
from datetime import datetime
from typing import List
from loguru import logger
from .parser import BiddingInfo


class MessageFormatter:
    """消息格式化器"""
    
    def __init__(self):
        self.max_message_length = 4096  # 企业微信消息长度限制
    
    def format_bidding_list_markdown(self, bidding_list: List[BiddingInfo], 
                                   title: str = None) -> str:
        """
        将招标信息列表格式化为Markdown格式
        
        Args:
            bidding_list (List[BiddingInfo]): 招标信息列表
            title (str): 消息标题
            
        Returns:
            str: 格式化后的Markdown消息
        """
        if not bidding_list:
            return self._format_empty_message()
        
        # 构建消息标题
        current_date = datetime.now().strftime('%Y年%m月%d日')
        message_title = title or f"📋 {current_date} 招标信息汇总"
        
        # 构建消息内容
        lines = [
            f"# {message_title}",
            "",
            f"📅 **统计时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"📊 **信息数量**: {len(bidding_list)} 条",
            "",
            "---",
            ""
        ]
        
        # 添加招标信息
        for i, bidding in enumerate(bidding_list, 1):
            item_content = self._format_single_bidding_markdown(bidding, i)
            lines.append(item_content)
            lines.append("")  # 添加空行分隔
        
        # 添加底部信息
        lines.extend([
            "---",
            f"🤖 *由AutoWebsite自动推送 | {datetime.now().strftime('%H:%M:%S')}*"
        ])
        
        message = "\n".join(lines)
        
        # 检查消息长度
        if len(message) > self.max_message_length:
            logger.warning(f"消息长度超限 ({len(message)} > {self.max_message_length})，进行截断")
            message = self._truncate_message(message, bidding_list)
        
        return message
    
    def _format_single_bidding_markdown(self, bidding: BiddingInfo, index: int) -> str:
        """格式化单条招标信息"""
        lines = [f"## {index}. {bidding.title}"]
        
        # 添加基本信息
        if bidding.publish_date:
            lines.append(f"📅 **发布时间**: {bidding.publish_date}")
        
        if bidding.category:
            lines.append(f"🏷️ **分类**: {bidding.category}")
        
        if bidding.location:
            lines.append(f"📍 **地区**: {bidding.location}")
        
        # 添加内容摘要
        if bidding.content:
            content = bidding.content[:100] + "..." if len(bidding.content) > 100 else bidding.content
            lines.append(f"📝 **摘要**: {content}")
        
        # 添加链接
        if bidding.link:
            lines.append(f"🔗 **详情链接**: [点击查看]({bidding.link})")
        
        return "\n".join(lines)
    
    def format_bidding_list_text(self, bidding_list: List[BiddingInfo], 
                               title: str = None) -> str:
        """
        将招标信息列表格式化为纯文本格式
        
        Args:
            bidding_list (List[BiddingInfo]): 招标信息列表
            title (str): 消息标题
            
        Returns:
            str: 格式化后的文本消息
        """
        if not bidding_list:
            return self._format_empty_message_text()
        
        current_date = datetime.now().strftime('%Y年%m月%d日')
        message_title = title or f"📋 {current_date} 招标信息汇总"
        
        lines = [
            message_title,
            "=" * 30,
            f"📅 统计时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"📊 信息数量: {len(bidding_list)} 条",
            "",
            "-" * 30,
            ""
        ]
        
        for i, bidding in enumerate(bidding_list, 1):
            item_content = self._format_single_bidding_text(bidding, i)
            lines.append(item_content)
            lines.append("-" * 20)
            lines.append("")
        
        lines.extend([
            f"🤖 由AutoWebsite自动推送 | {datetime.now().strftime('%H:%M:%S')}"
        ])
        
        message = "\n".join(lines)
        
        if len(message) > self.max_message_length:
            logger.warning(f"文本消息长度超限，进行截断")
            message = self._truncate_text_message(message, bidding_list)
        
        return message
    
    def _format_single_bidding_text(self, bidding: BiddingInfo, index: int) -> str:
        """格式化单条招标信息为文本"""
        lines = [f"{index}. {bidding.title}"]
        
        if bidding.publish_date:
            lines.append(f"   发布时间: {bidding.publish_date}")
        
        if bidding.category:
            lines.append(f"   分类: {bidding.category}")
        
        if bidding.location:
            lines.append(f"   地区: {bidding.location}")
        
        if bidding.content:
            content = bidding.content[:80] + "..." if len(bidding.content) > 80 else bidding.content
            lines.append(f"   摘要: {content}")
        
        if bidding.link:
            lines.append(f"   链接: {bidding.link}")
        
        return "\n".join(lines)
    
    def format_news_articles(self, bidding_list: List[BiddingInfo]) -> List[dict]:
        """
        将招标信息格式化为图文消息格式
        
        Args:
            bidding_list (List[BiddingInfo]): 招标信息列表
            
        Returns:
            List[dict]: 图文消息列表
        """
        articles = []
        
        # 企业微信图文消息最多8条
        max_articles = min(8, len(bidding_list))
        
        for i, bidding in enumerate(bidding_list[:max_articles]):
            # 构建描述
            description_parts = []
            if bidding.publish_date:
                description_parts.append(f"发布时间: {bidding.publish_date}")
            if bidding.category:
                description_parts.append(f"分类: {bidding.category}")
            if bidding.location:
                description_parts.append(f"地区: {bidding.location}")
            
            description = " | ".join(description_parts)
            
            article = {
                "title": bidding.title[:64],  # 标题长度限制
                "description": description[:120],  # 描述长度限制
                "url": bidding.link,
                "picurl": ""  # 可以添加默认图片URL
            }
            articles.append(article)
        
        return articles
    
    def _format_empty_message(self) -> str:
        """格式化空消息"""
        current_date = datetime.now().strftime('%Y年%m月%d日')
        return f"""# 📋 {current_date} 招标信息汇总

📅 **统计时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📊 **信息数量**: 0 条

---

🔍 今日暂无新的招标信息

---
🤖 *由AutoWebsite自动推送 | {datetime.now().strftime('%H:%M:%S')}*"""
    
    def _format_empty_message_text(self) -> str:
        """格式化空消息文本版"""
        current_date = datetime.now().strftime('%Y年%m月%d日')
        return f"""📋 {current_date} 招标信息汇总
==============================
📅 统计时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📊 信息数量: 0 条

------------------------------
🔍 今日暂无新的招标信息

🤖 由AutoWebsite自动推送 | {datetime.now().strftime('%H:%M:%S')}"""
    
    def _truncate_message(self, message: str, bidding_list: List[BiddingInfo]) -> str:
        """截断过长的Markdown消息"""
        # 计算头部和尾部固定内容的长度
        header_lines = message.split('\n')[:7]  # 标题部分
        footer_lines = ["---", f"🤖 *由AutoWebsite自动推送 | {datetime.now().strftime('%H:%M:%S')}*"]
        
        header = "\n".join(header_lines)
        footer = "\n".join(footer_lines)
        
        available_length = self.max_message_length - len(header) - len(footer) - 100  # 预留缓冲
        
        # 重新构建消息，只包含能放下的条目
        content_lines = []
        current_length = 0
        
        for i, bidding in enumerate(bidding_list, 1):
            item_content = self._format_single_bidding_markdown(bidding, i)
            item_length = len(item_content) + 2  # 加上换行符
            
            if current_length + item_length > available_length:
                remaining_count = len(bidding_list) - i + 1
                content_lines.append(f"\n📝 *还有 {remaining_count} 条信息，请查看原始网站获取完整信息*")
                break
            
            content_lines.append(item_content)
            content_lines.append("")
            current_length += item_length
        
        return "\n".join([header, ""] + content_lines + [""] + footer_lines)
    
    def _truncate_text_message(self, message: str, bidding_list: List[BiddingInfo]) -> str:
        """截断过长的文本消息"""
        lines = message.split('\n')
        header_lines = lines[:7]
        footer_line = lines[-1]
        
        header = "\n".join(header_lines)
        available_length = self.max_message_length - len(header) - len(footer_line) - 100
        
        content_lines = []
        current_length = 0
        
        for i, bidding in enumerate(bidding_list, 1):
            item_content = self._format_single_bidding_text(bidding, i)
            item_length = len(item_content) + 25  # 加上分隔符
            
            if current_length + item_length > available_length:
                remaining_count = len(bidding_list) - i + 1
                content_lines.append(f"\n📝 还有 {remaining_count} 条信息，请查看原始网站")
                break
            
            content_lines.append(item_content)
            content_lines.append("-" * 20)
            content_lines.append("")
            current_length += item_length
        
        return "\n".join([header, ""] + content_lines + [footer_line])
