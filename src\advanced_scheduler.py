"""
高级多URL多配置调度器
支持为不同的URL使用不同的解析配置
"""
import schedule
import time
from datetime import datetime
from loguru import logger
from .config import Config, setup_logging
from .scraper import WebScraper
from .parser import HTMLParser, SELECTOR_CONFIGS
from .wechat_api import WeChatAPI
from .formatter import MessageFormatter
from .multi_site_configs import setup_multi_site_configs, get_config_for_url, URL_CONFIG_MAPPING


class AdvancedBiddingBot:
    """高级多URL多配置招标信息推送机器人"""
    
    def __init__(self, url_config_mapping: dict = None):
        """
        初始化机器人
        
        Args:
            url_config_mapping (dict): URL到配置的映射，格式: {url: config_name}
        """
        # 设置日志
        setup_logging()
        
        # 验证配置
        Config.validate()
        
        # 设置多网站配置
        setup_multi_site_configs()
        
        # 初始化组件
        self.scraper = WebScraper()
        self.parser = HTMLParser()
        self.wechat_api = WeChatAPI()
        self.formatter = MessageFormatter()
        
        # URL配置映射
        self.url_config_mapping = url_config_mapping or URL_CONFIG_MAPPING
        
        logger.info("高级多URL多配置招标信息推送机器人初始化完成")
    
    def run_once(self) -> bool:
        """
        执行一次完整的抓取和推送流程
        
        Returns:
            bool: 执行是否成功
        """
        try:
            logger.info("开始执行高级多URL招标信息抓取推送任务")
            
            # 获取所有目标URL
            target_urls = Config.get_target_urls()
            logger.info(f"准备抓取 {len(target_urls)} 个网页")
            
            all_bidding_list = []
            success_count = 0
            
            # 1. 抓取所有网页数据
            for i, url in enumerate(target_urls, 1):
                logger.info(f"抓取第 {i}/{len(target_urls)} 个网页: {url}")
                
                try:
                    # 获取该URL对应的配置
                    config_name = self._get_config_for_url(url)
                    config = SELECTOR_CONFIGS.get(config_name, SELECTOR_CONFIGS['default'])
                    logger.info(f"使用配置: {config_name}")
                    
                    # 抓取网页
                    html_content = self.scraper.fetch_page(url)
                    if not html_content:
                        logger.warning(f"第 {i} 个网页抓取失败: {url}")
                        continue
                    
                    # 解析招标信息
                    bidding_list = self.parser.extract_bidding_info(html_content, config)
                    if bidding_list:
                        logger.info(f"第 {i} 个网页解析到 {len(bidding_list)} 条信息")
                        # 为每条信息添加来源标记
                        for bidding in bidding_list:
                            if not hasattr(bidding, 'source'):
                                bidding.source = url
                        all_bidding_list.extend(bidding_list)
                        success_count += 1
                    else:
                        logger.warning(f"第 {i} 个网页未解析到招标信息")
                        
                except Exception as e:
                    logger.error(f"处理第 {i} 个网页时发生错误: {e}")
                    continue
            
            logger.info(f"成功处理 {success_count}/{len(target_urls)} 个网页")
            
            if not all_bidding_list:
                logger.warning("所有网页均未解析到招标信息")
                # 发送空消息通知
                empty_message = self.formatter.format_bidding_list_markdown([])
                self.wechat_api.send_markdown_message(empty_message)
                return True
            
            logger.info(f"总共解析到 {len(all_bidding_list)} 条招标信息")
            
            # 2. 去重（基于标题和链接）
            unique_bidding_list = self._remove_duplicates(all_bidding_list)
            if len(unique_bidding_list) < len(all_bidding_list):
                logger.info(f"去重后剩余 {len(unique_bidding_list)} 条信息")
            
            # 3. 按日期排序（最新的在前）
            unique_bidding_list.sort(key=lambda x: x.publish_date, reverse=True)
            
            # 4. 过滤今日数据
            today_bidding = self.parser.filter_today_data(unique_bidding_list)
            
            # 5. 格式化消息
            if today_bidding:
                message = self.formatter.format_bidding_list_markdown(
                    today_bidding, 
                    f"今日招标信息汇总（{len(target_urls)}个网站）"
                )
                logger.info(f"准备推送 {len(today_bidding)} 条今日招标信息")
                for bidding in today_bidding:
                    logger.info(f"解析到招标信息: {bidding}")
            else:
                # 如果没有今日信息，发送最新的几条作为参考
                recent_bidding = unique_bidding_list[:5]  # 最新5条
                message = self.formatter.format_bidding_list_markdown(
                    recent_bidding, 
                    f"最新招标信息（{len(target_urls)}个网站，今日暂无新信息）"
                )
                logger.info("今日无新招标信息，发送最新信息通知")
            
            # 6. 发送消息
            success = self.wechat_api.send_markdown_message(message)
            
            if success:
                logger.info("招标信息推送完成")
                return True
            else:
                logger.error("消息发送失败")
                return False
                
        except Exception as e:
            logger.error(f"执行任务时发生错误: {e}")
            # 发送错误通知
            error_message = f"⚠️ 高级多URL招标信息推送任务执行失败\n\n错误信息: {str(e)}\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            try:
                self.wechat_api.send_text_message(error_message)
            except:
                pass
            return False
    
    def _get_config_for_url(self, url: str) -> str:
        """获取URL对应的配置名称"""
        # 首先检查手动映射
        if url in self.url_config_mapping:
            return self.url_config_mapping[url]
        
        # 然后使用自动检测
        return get_config_for_url(url)
    
    def _remove_duplicates(self, bidding_list):
        """去除重复的招标信息"""
        seen = set()
        unique_list = []
        
        for bidding in bidding_list:
            # 使用标题和链接作为唯一标识
            key = (bidding.title, bidding.link)
            if key not in seen:
                seen.add(key)
                unique_list.append(bidding)
        
        return unique_list
    
    def test_all_components(self) -> bool:
        """测试所有组件"""
        logger.info("开始测试所有组件")
        
        try:
            # 测试企业微信连接
            logger.info("测试企业微信连接...")
            if not self.wechat_api.test_connection():
                logger.error("企业微信连接测试失败")
                return False
            
            # 测试网页抓取
            target_urls = Config.get_target_urls()
            logger.info(f"测试 {len(target_urls)} 个网页抓取...")
            
            test_results = []
            total_items = 0
            
            for i, url in enumerate(target_urls, 1):
                logger.info(f"测试第 {i}/{len(target_urls)} 个网页: {url}")
                
                config_name = self._get_config_for_url(url)
                config = SELECTOR_CONFIGS.get(config_name, SELECTOR_CONFIGS['default'])
                
                html_content = self.scraper.fetch_page(url)
                if html_content:
                    bidding_list = self.parser.extract_bidding_info(html_content, config)
                    total_items += len(bidding_list)
                    test_results.append(f"✅ 网站{i}: {len(bidding_list)}条 (配置: {config_name})")
                    logger.info(f"第 {i} 个网页解析到 {len(bidding_list)} 条数据，使用配置: {config_name}")
                else:
                    test_results.append(f"❌ 网站{i}: 抓取失败")
                    logger.warning(f"第 {i} 个网页抓取失败")
            
            # 发送测试消息
            logger.info("发送测试消息...")
            test_message = f"""# 🧪 高级多URL系统测试报告

📅 **测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 测试结果
✅ **企业微信连接**: 正常
✅ **网页抓取**: {len(target_urls)} 个网站
✅ **数据解析**: 总计 {total_items} 条数据

## 详细结果
{chr(10).join(test_results)}

## 网站配置映射
{chr(10).join([f"{i}. {url} → {self._get_config_for_url(url)}" for i, url in enumerate(target_urls, 1)])}

---
🤖 *Advanced AutoWebsite系统测试完成*"""
            
            success = self.wechat_api.send_markdown_message(test_message)
            
            if success:
                logger.info("所有组件测试通过")
                return True
            else:
                logger.error("测试消息发送失败")
                return False
                
        except Exception as e:
            logger.error(f"组件测试时发生错误: {e}")
            return False
    
    def start_scheduler(self):
        """启动定时任务"""
        target_urls = Config.get_target_urls()
        logger.info(f"启动高级定时任务，执行时间: {Config.SCHEDULE_TIME}，监控 {len(target_urls)} 个网站")
        
        # 设置定时任务
        schedule.every().day.at(Config.SCHEDULE_TIME).do(self.run_once)
        
        # 发送启动通知
        config_info = []
        for url in target_urls:
            config_name = self._get_config_for_url(url)
            config_info.append(f"• {url} (配置: {config_name})")
        
        start_message = f"""# 🚀 高级多URL招标信息推送服务启动

📅 **启动时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
⏰ **推送时间**: 每天 {Config.SCHEDULE_TIME}
🎯 **监控网站**: {len(target_urls)} 个

## 网站配置
{chr(10).join(config_info)}

---
🤖 *高级服务已启动，将自动推送招标信息*"""
        
        try:
            self.wechat_api.send_markdown_message(start_message)
        except Exception as e:
            logger.warning(f"启动通知发送失败: {e}")
        
        # 运行调度器
        logger.info("定时任务调度器开始运行")
        while True:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except KeyboardInterrupt:
                logger.info("收到停止信号，正在关闭...")
                break
            except Exception as e:
                logger.error(f"调度器运行时发生错误: {e}")
                time.sleep(300)  # 出错后等待5分钟再继续
    
    def stop(self):
        """停止服务"""
        logger.info("正在停止服务...")
        
        # 关闭网络会话
        if hasattr(self, 'scraper'):
            self.scraper.close()
        
        # 发送停止通知
        stop_message = f"""# 🛑 高级多URL招标信息推送服务停止

📅 **停止时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

---
🤖 *服务已停止*"""
        
        try:
            self.wechat_api.send_markdown_message(stop_message)
        except Exception as e:
            logger.warning(f"停止通知发送失败: {e}")
        
        logger.info("服务已停止")
