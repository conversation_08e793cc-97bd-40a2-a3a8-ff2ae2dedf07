"""
数据解析模块测试
"""
import unittest
from datetime import date
from src.parser import HTMLParser, BiddingInfo, SELECTOR_CONFIGS


class TestHTMLParser(unittest.TestCase):
    """HTML解析器测试"""
    
    def setUp(self):
        self.parser = HTMLParser()
    
    def test_parse_html(self):
        """测试HTML解析"""
        html = "<html><body><h1>Test</h1></body></html>"
        soup = self.parser.parse_html(html)
        self.assertIsNotNone(soup)
        self.assertEqual(soup.find('h1').text, 'Test')
    
    def test_extract_date(self):
        """测试日期提取"""
        test_cases = [
            ("2024-01-15", "2024-01-15"),
            ("2024/01/15", "2024/01/15"),
            ("2024.01.15", "2024.01.15"),
            ("发布时间：2024-01-15", "2024-01-15"),
        ]
        
        for input_text, expected in test_cases:
            with self.subTest(input_text=input_text):
                result = self.parser._extract_date(input_text)
                self.assertEqual(result, expected)
    
    def test_normalize_date(self):
        """测试日期标准化"""
        test_cases = [
            ("2024-01-15", "2024-01-15"),
            ("2024/01/15", "2024-01-15"),
            ("01-15-2024", "2024-01-15"),
        ]
        
        for input_date, expected in test_cases:
            with self.subTest(input_date=input_date):
                result = self.parser._normalize_date(input_date)
                self.assertEqual(result, expected)
    
    def test_is_today(self):
        """测试今日数据判断"""
        today_str = date.today().strftime('%Y-%m-%d')
        
        # 测试今天的日期
        self.assertTrue(self.parser._is_today(today_str, today_str))
        
        # 测试昨天的日期
        self.assertFalse(self.parser._is_today("2020-01-01", today_str))
    
    def test_extract_bidding_info_with_mock_html(self):
        """测试招标信息提取（使用模拟HTML）"""
        mock_html = """
        <html>
        <body>
            <table>
                <tr>
                    <td><a href="/detail/1">测试招标项目1</a></td>
                    <td>2024-01-15</td>
                </tr>
                <tr>
                    <td><a href="/detail/2">测试招标项目2</a></td>
                    <td>2024-01-16</td>
                </tr>
            </table>
        </body>
        </html>
        """
        
        selectors = {
            'item_selector': 'tr',
            'title_selector': 'a',
            'date_selector': 'td:last-child',
            'base_url': 'https://example.com'
        }
        
        bidding_list = self.parser.extract_bidding_info(mock_html, selectors)
        
        self.assertEqual(len(bidding_list), 2)
        self.assertEqual(bidding_list[0].title, "测试招标项目1")
        self.assertEqual(bidding_list[0].link, "https://example.com/detail/1")
        self.assertEqual(bidding_list[0].publish_date, "2024-01-15")


class TestBiddingInfo(unittest.TestCase):
    """招标信息数据类测试"""
    
    def test_bidding_info_creation(self):
        """测试招标信息创建"""
        bidding = BiddingInfo(
            title="测试项目",
            link="https://example.com/1",
            publish_date="2024-01-15",
            content="测试内容",
            category="工程建设",
            location="北京市"
        )
        
        self.assertEqual(bidding.title, "测试项目")
        self.assertEqual(bidding.link, "https://example.com/1")
        self.assertEqual(bidding.publish_date, "2024-01-15")
        self.assertEqual(bidding.content, "测试内容")
        self.assertEqual(bidding.category, "工程建设")
        self.assertEqual(bidding.location, "北京市")
    
    def test_to_dict(self):
        """测试转换为字典"""
        bidding = BiddingInfo(
            title="测试项目",
            link="https://example.com/1",
            publish_date="2024-01-15"
        )
        
        result = bidding.to_dict()
        expected = {
            'title': "测试项目",
            'link': "https://example.com/1",
            'publish_date': "2024-01-15",
            'content': "",
            'category': "",
            'location': ""
        }
        
        self.assertEqual(result, expected)


if __name__ == '__main__':
    unittest.main()
