"""
多网站配置支持
为不同的网站提供不同的解析配置
"""
from .parser import SELECTOR_CONFIGS

# 西矿集团不同栏目的配置
WESTMINING_CONFIGS = {
    'westmining_bidding': {
        'item_selector': 'li[name="li_name"]',
        'title_selector': 'a',
        'date_selector': 'a em:nth-of-type(2)',
        'base_url': 'https://ec.westmining.com'
    },
    'westmining_notice': {
        'item_selector': 'li[name="li_name"]',
        'title_selector': 'a',
        'date_selector': 'a em:nth-of-type(2)',
        'base_url': 'https://ec.westmining.com'
    },
    'westmining_result': {
        'item_selector': 'li[name="li_name"]',
        'title_selector': 'a',
        'date_selector': 'a em:nth-of-type(2)',
        'base_url': 'https://ec.westmining.com'
    }
}

# 其他常见招标网站配置
OTHER_SITE_CONFIGS = {
    'ccgp_gov': {
        'item_selector': '.vT-srch-result-list-bid .vT-srch-result-list-bid-gg',
        'title_selector': 'a',
        'date_selector': '.vT-srch-result-list-bid-gg-date',
        'category_selector': '.vT-srch-result-list-bid-gg-type',
        'location_selector': '.vT-srch-result-list-bid-gg-area',
        'base_url': 'http://www.ccgp.gov.cn'
    },
    'province_center': {
        'item_selector': '.ewb-list-node',
        'title_selector': '.ewb-list-node-title a',
        'date_selector': '.ewb-list-node-time',
        'category_selector': '.ewb-list-node-type',
        'location_selector': '.ewb-list-node-area',
        'content_selector': '.ewb-list-node-summary',
        'base_url': 'https://example-province.gov.cn'
    },
    'table_style': {
        'item_selector': 'tr',
        'title_selector': 'td:nth-child(2) a',
        'date_selector': 'td:nth-child(4)',
        'location_selector': 'td:nth-child(3)',
        'base_url': 'https://example-table-site.com'
    },
    'southmn_site': {
        'item_selector': 'tr',
        'title_selector': '.gs_txt1 a',
        'date_selector': 'td:nth-child(2)',
        'base_url': 'http://mn.southmn.com'
    },
    'westmining_official': {
        'item_selector': '.item',
        'title_selector': 'a',
        'date_selector': '.date',
        'base_url': 'https://www.westmininggroup.com'
    }
}

def setup_multi_site_configs():
    """将多网站配置添加到系统中"""
    # 添加西矿集团配置
    SELECTOR_CONFIGS.update(WESTMINING_CONFIGS)
    
    # 添加其他网站配置
    SELECTOR_CONFIGS.update(OTHER_SITE_CONFIGS)
    
    print(f"已添加 {len(WESTMINING_CONFIGS) + len(OTHER_SITE_CONFIGS)} 个网站配置")

def get_config_for_url(url: str) -> str:
    """根据URL自动选择配置"""
    if 'ec.westmining.com' in url:
        if 'ywgg1' in url:  # 招标公告
            return 'westmining_bidding'
        elif 'ywgg2' in url:  # 中标公告
            return 'westmining_notice'
        elif 'ywgg3' in url:  # 中标结果
            return 'westmining_result'
        else:
            return 'westmining_bidding'  # 默认
    elif 'www.westmininggroup.com' in url:
        return 'westmining_official'
    elif 'mn.southmn.com' in url:
        return 'southmn_site'
    elif 'ccgp.gov.cn' in url:
        return 'ccgp_gov'
    elif 'province' in url:
        return 'province_center'
    else:
        return 'default'

# URL到配置的映射示例
URL_CONFIG_MAPPING = {
    'https://ec.westmining.com/cms/channel/1ywgg1/index.htm': 'westmining_bidding',
    'https://ec.westmining.com/cms/channel/1ywgg2/index.htm': 'westmining_notice',
    'https://ec.westmining.com/cms/channel/1ywgg3/index.htm': 'westmining_result',
    'http://mn.southmn.com/list/?368_1.html': 'southmn_site',
    # 注意：西矿集团官网使用iframe嵌入，建议直接使用iframe中的URL
    # 'https://www.westmininggroup.com/tzgg/zbgg1/': 'westmining_official',
    'http://www.ccgp.gov.cn/cggg/zygg/': 'ccgp_gov',
}

if __name__ == '__main__':
    setup_multi_site_configs()
    print("可用配置:", list(SELECTOR_CONFIGS.keys()))
