"""
数据解析模块
"""
import re
from datetime import datetime, date
from bs4 import BeautifulSoup
from loguru import logger
from typing import List, Dict, Optional


class BiddingInfo:
    """招标信息数据类"""
    
    def __init__(self, title: str, link: str, publish_date: str, 
                 content: str = "", category: str = "", location: str = ""):
        self.title = title
        self.link = link
        self.publish_date = publish_date
        self.content = content
        self.category = category
        self.location = location
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'title': self.title,
            'link': self.link,
            'publish_date': self.publish_date,
            'content': self.content,
            'category': self.category,
            'location': self.location
        }
    
    def __str__(self):
        return f"招标信息: {self.title} ({self.publish_date})"


class HTMLParser:
    """HTML解析器"""
    
    def __init__(self):
        self.today = date.today()
    
    def parse_html(self, html_content: str) -> BeautifulSoup:
        """解析HTML内容"""
        try:
            soup = BeautifulSoup(html_content, 'lxml')
            logger.debug("HTML解析成功")
            return soup
        except Exception as e:
            logger.error(f"HTML解析失败: {e}")
            return None
    
    def extract_bidding_info(self, html_content: str, selectors: Dict) -> List[BiddingInfo]:
        """
        提取招标信息
        
        Args:
            html_content (str): HTML内容
            selectors (dict): CSS选择器配置
            
        Returns:
            List[BiddingInfo]: 招标信息列表
        """
        soup = self.parse_html(html_content)
        if not soup:
            return []
        
        bidding_list = []
        
        try:
            # 查找招标信息容器
            items = soup.select(selectors.get('item_selector', 'tr'))
            logger.info(f"找到 {len(items)} 条记录")
            
            for item in items:
                try:
                    # 提取标题和链接
                    title_element = item.select_one(selectors.get('title_selector', 'a'))
                    if not title_element:
                        continue

                    # 获取标题文本，优先从title属性获取，否则从文本内容获取
                    title = title_element.get('title', '').strip()
                    if not title:
                        title = title_element.get_text(strip=True)

                    # 清理标题中的多余信息（如"招标公告"等后缀）
                    if title.endswith('招标公告'):
                        title = title.replace('招标公告', '').strip()
                    if title.endswith('  招标公告'):
                        title = title.replace('  招标公告', '').strip()

                    link = title_element.get('href', '')
                    
                    # 处理相对链接
                    if link and not link.startswith('http'):
                        base_url = selectors.get('base_url', '')
                        link = base_url.rstrip('/') + '/' + link.lstrip('/')
                    
                    # 提取发布日期
                    date_element = item.select_one(selectors.get('date_selector', 'td:last-child'))
                    publish_date = ''
                    if date_element:
                        publish_date = self._extract_date(date_element.get_text(strip=True))

                    # 如果没有找到日期，尝试在所有em元素中查找
                    if not publish_date:
                        em_elements = item.select('em')
                        for em in em_elements:
                            em_text = em.get_text(strip=True)
                            if em_text and len(em_text) >= 8:  # 日期至少8个字符
                                extracted_date = self._extract_date(em_text)
                                if extracted_date:
                                    publish_date = extracted_date
                                    break
                    
                    # 提取其他信息
                    category = self._extract_category(item, selectors)
                    location = self._extract_location(item, selectors)
                    content = self._extract_content(item, selectors)
                    
                    if title and publish_date:
                        bidding_info = BiddingInfo(
                            title=title,
                            link=link,
                            publish_date=publish_date,
                            content=content,
                            category=category,
                            location=location
                        )
                        bidding_list.append(bidding_info)
                        
                except Exception as e:
                    logger.warning(f"解析单条记录失败: {e}")
                    continue
            
            logger.info(f"成功解析 {len(bidding_list)} 条招标信息")
            return bidding_list
            
        except Exception as e:
            logger.error(f"提取招标信息失败: {e}")
            return []
    
    def _extract_date(self, date_text: str) -> str:
        """提取日期"""
        # 常见日期格式的正则表达式
        date_patterns = [
            r'(\d{4}-\d{2}-\d{2})',
            r'(\d{4}/\d{2}/\d{2})',
            r'(\d{4}\.\d{2}\.\d{2})',
            r'(\d{2}-\d{2}-\d{2})',
            r'(\d{2}/\d{2}/\d{2})',
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, date_text)
            if match:
                return match.group(1)
        
        return date_text.strip()
    
    def _extract_category(self, item, selectors: Dict) -> str:
        """提取分类信息"""
        category_selector = selectors.get('category_selector')
        if category_selector:
            category_element = item.select_one(category_selector)
            if category_element:
                return category_element.get_text(strip=True)
        return ""
    
    def _extract_location(self, item, selectors: Dict) -> str:
        """提取地区信息"""
        location_selector = selectors.get('location_selector')
        if location_selector:
            location_element = item.select_one(location_selector)
            if location_element:
                return location_element.get_text(strip=True)
        return ""
    
    def _extract_content(self, item, selectors: Dict) -> str:
        """提取内容摘要"""
        content_selector = selectors.get('content_selector')
        if content_selector:
            content_element = item.select_one(content_selector)
            if content_element:
                return content_element.get_text(strip=True)
        return ""
    
    def filter_today_data(self, bidding_list: List[BiddingInfo]) -> List[BiddingInfo]:
        """过滤今天发布的数据"""
        today_str = self.today.strftime('%Y-%m-%d')
        today_data = []
        
        for bidding in bidding_list:
            if self._is_today(bidding.publish_date, today_str):
                today_data.append(bidding)
        
        logger.info(f"过滤出今日数据 {len(today_data)} 条")
        return today_data
    
    def _is_today(self, publish_date: str, today_str: str) -> bool:
        """判断是否为今天的数据"""
        if not publish_date:
            return False
        
        # 标准化日期格式
        normalized_date = self._normalize_date(publish_date)
        return normalized_date == today_str
    
    def _normalize_date(self, date_str: str) -> str:
        """标准化日期格式为 YYYY-MM-DD"""
        try:
            # 尝试不同的日期格式
            formats = [
                '%Y-%m-%d',
                '%Y/%m/%d',
                '%Y.%m.%d',
                '%m-%d-%Y',
                '%m/%d/%Y',
                '%d-%m-%Y',
                '%d/%m/%Y'
            ]
            
            for fmt in formats:
                try:
                    dt = datetime.strptime(date_str, fmt)
                    return dt.strftime('%Y-%m-%d')
                except ValueError:
                    continue
            
            # 如果都不匹配，返回原字符串
            return date_str
            
        except Exception as e:
            logger.warning(f"日期格式化失败: {date_str}, 错误: {e}")
            return date_str


# 常用网站的选择器配置示例
SELECTOR_CONFIGS = {
    'default': {
        'item_selector': 'tr',
        'title_selector': 'a',
        'date_selector': 'td:last-child',
        'base_url': ''
    },
    'example_site': {
        'item_selector': '.list-item',
        'title_selector': '.title a',
        'date_selector': '.date',
        'category_selector': '.category',
        'location_selector': '.location',
        'content_selector': '.content',
        'base_url': 'https://example.com'
    },
    'mining_bidding': {
        'item_selector': 'li[name="li_name"]',
        'title_selector': 'a',
        'date_selector': 'a em:nth-of-type(2)',
        'base_url': ''  # 需要根据实际网站填写
    },
    'mining_site': {
        'item_selector': 'li[name="li_name"]',
        'title_selector': 'a',
        'date_selector': 'a em:nth-of-type(2)',
        'base_url': 'https://ec.westmining.com'
    }
}
