#!/usr/bin/env python3
"""
测试实际网站的完整解析流程
"""
from src.scraper import WebScraper
from src.parser import HTMLParser, SELECTOR_CONFIGS
from src.formatter import MessageFormatter

def test_real_parsing():
    """测试实际网站的完整解析流程"""
    url = "https://ec.westmining.com/cms/channel/1ywgg1/index.htm"
    
    print(f"🚀 开始测试实际网站解析: {url}")
    
    # 1. 抓取网页
    print("\n📡 步骤1: 抓取网页...")
    scraper = WebScraper()
    html_content = scraper.fetch_page(url)
    
    if not html_content:
        print("❌ 网页抓取失败")
        return False
    
    print(f"✅ 网页抓取成功，内容长度: {len(html_content)}")
    
    # 2. 解析招标信息
    print("\n🔍 步骤2: 解析招标信息...")
    parser = HTMLParser()
    
    # 获取mining_site配置
    config = SELECTOR_CONFIGS.get('mining_site')
    if not config:
        print("❌ 未找到mining_site配置")
        return False
    
    print(f"使用配置: {config}")
    
    # 解析HTML
    bidding_list = parser.extract_bidding_info(html_content, config)
    
    print(f"解析结果: 共 {len(bidding_list)} 条招标信息")
    
    if not bidding_list:
        print("❌ 未解析到任何招标信息")
        return False
    
    # 显示解析结果
    for i, bidding in enumerate(bidding_list, 1):
        print(f"\n第 {i} 条:")
        print(f"  标题: {bidding.title}")
        print(f"  链接: {bidding.link}")
        print(f"  日期: {bidding.publish_date}")
        print(f"  分类: {bidding.category}")
        print(f"  地区: {bidding.location}")
    
    # 3. 过滤今日数据
    print(f"\n📅 步骤3: 过滤今日数据...")
    today_bidding = parser.filter_today_data(bidding_list)
    print(f"今日数据: {len(today_bidding)} 条")
    
    for bidding in today_bidding:
        print(f"  今日: {bidding.title} ({bidding.publish_date})")
    
    # 4. 格式化消息
    print(f"\n💬 步骤4: 格式化消息...")
    formatter = MessageFormatter()
    
    # 使用所有数据进行格式化（模拟推送效果）
    if today_bidding:
        message = formatter.format_bidding_list_markdown(today_bidding, "今日招标信息")
        print("今日招标信息Markdown格式:")
    else:
        message = formatter.format_bidding_list_markdown(bidding_list[:3], "最新招标信息（前3条）")
        print("最新招标信息Markdown格式:")
    
    print("=" * 60)
    print(message)
    print("=" * 60)
    
    # 5. 测试完整链接
    print(f"\n🔗 步骤5: 测试完整链接...")
    base_url = "https://ec.westmining.com"
    
    for i, bidding in enumerate(bidding_list[:3], 1):
        if bidding.link.startswith('/'):
            full_link = base_url + bidding.link
            print(f"  第{i}条完整链接: {full_link}")
        else:
            print(f"  第{i}条链接: {bidding.link}")
    
    scraper.close()
    
    print(f"\n✅ 测试完成！成功解析 {len(bidding_list)} 条招标信息")
    return True

if __name__ == '__main__':
    # 确保mining_site配置存在
    from config_mining_site import setup_mining_site_config
    setup_mining_site_config()
    
    success = test_real_parsing()
    if success:
        print("\n🎉 实际网站解析测试成功！")
        print("\n📋 下一步操作:")
        print("1. 配置企业微信参数")
        print("2. 运行: python main.py --config mining_site --test")
        print("3. 运行: python main.py --config mining_site --once")
    else:
        print("\n❌ 测试失败，请检查配置")
