#!/usr/bin/env python3
"""
测试矿业招标网站解析
"""
from src.parser import HTMLParser, SELECTOR_CONFIGS

# 您提供的HTML示例
html_content = '''
		<div class="infolist">

				<!--  列表页tab选项  -->

				<!--  招标信息列表  -->
			<!--  全部招标信息列表  -->

			<!-- 其他栏目 -->
			<div class="infolist-main bidlist bidlist2" style="border-top: 2px solid #D70100;">
				<ul id="list1">
				
						<!-- 当channel??&&channel.id==241时候显示241下所有栏目信息-->								
							<li name="li_name">
								<a id="0" href="/cms/channel/1ywgg1/29783.htm" title="青海西矿杭萧钢构有限公司2025年废钢销售项目【重新招标】销售公告  招标公告"  target="_blank" rel="noopener noreferrer" style="">
									<span>
										<i class="iconfont">&#xe638;</i>
										<em style="width:6.5em; color: #1e52a8;font-weight: 700;float: none;">
											
												<!-- 采购方式 -->

										</em>
										青海西矿杭萧钢构有限公司2025年废钢销售项目【重新招标】销售公告
									</span>
									<em>
											2025-07-02
									</em>
									<!-- 状态 -->
									<span class="style">
										<input type="hidden" bmstart_1="2025-07-02 16:53:00" bmend_1="2025-07-02 16:53:00"
												buystart_1="2025-07-03 08:30:00" buyend_1="2025-07-07 18:00:00" class="endTime1" 
												bmstart_2="" bmend_2=""
												buystart_2="" buyend_2="" >
									</span>
								</a>
							</li>
							<li name="li_name">
								<a id="1" href="/cms/channel/1ywgg1/29724.htm" title="西藏玉龙铜业股份有限公司2025年硫化钠零库存采购项目招标公告  招标公告"  target="_blank" rel="noopener noreferrer" style="">
									<span>
										<i class="iconfont">&#xe638;</i>
										<em style="width:6.5em; color: #1e52a8;font-weight: 700;float: none;">
											
												<!-- 采购方式 -->

										</em>
										西藏玉龙铜业股份有限公司2025年硫化钠零库存采购项目招标公告
									</span>
									<em>
											2025-07-01
									</em>
									<!-- 状态 -->
									<span class="style">
										<input type="hidden" bmstart_1="2025-07-01 14:27:00" bmend_1="2025-07-01 14:27:00"
												buystart_1="2025-07-01 14:30:00" buyend_1="2025-07-07 18:00:00" class="endTime1" 
												bmstart_2="" bmend_2=""
												buystart_2="" buyend_2="" >
									</span>
								</a>
							</li>
							<li name="li_name">
								<a id="2" href="/cms/channel/1ywgg1/29713.htm" title="肃北县博伦矿业开发有限责任公司七角井铁矿选矿厂技术提升改造工程渣浆泵设备采购项目【重新招标】招标公告  招标公告"  target="_blank" rel="noopener noreferrer" style="">
									<span>
										<i class="iconfont">&#xe638;</i>
										<em style="width:6.5em; color: #1e52a8;font-weight: 700;float: none;">
											
												<!-- 采购方式 -->

										</em>
										肃北县博伦矿业开发有限责任公司七角井铁矿选矿厂技术提升改造工程渣浆泵设备采购项目【重新招标】招标公告
									</span>
									<em>
											2025-07-01
									</em>
									<!-- 状态 -->
									<span class="style">
										<input type="hidden" bmstart_1="2025-07-01 09:04:00" bmend_1="2025-07-01 09:04:00"
												buystart_1="2025-07-01 09:30:00" buyend_1="2025-07-07 18:00:00" class="endTime1" 
												bmstart_2="" bmend_2=""
												buystart_2="" buyend_2="" >
									</span>
								</a>
							</li>
							<li name="li_name">
								<a id="3" href="/cms/channel/1ywgg1/29698.htm" title="肃北县博伦矿业开发有限责任公司七角井铁矿选矿厂技术提升改造工程带式输送机设备采购项目【重新招标】招标公告  招标公告"  target="_blank" rel="noopener noreferrer" style="">
									<span>
										<i class="iconfont">&#xe638;</i>
										<em style="width:6.5em; color: #1e52a8;font-weight: 700;float: none;">
											
												<!-- 采购方式 -->

										</em>
										肃北县博伦矿业开发有限责任公司七角井铁矿选矿厂技术提升改造工程带式输送机设备采购项目【重新招标】招标公告
									</span>
									<em>
											2025-06-30
									</em>
									<!-- 状态 -->
									<span class="style">
										<input type="hidden" bmstart_1="2025-06-30 17:24:00" bmend_1="2025-06-30 17:24:00"
												buystart_1="2025-07-01 00:00:00" buyend_1="2025-07-07 23:59:00" class="endTime1" 
												bmstart_2="" bmend_2=""
												buystart_2="" buyend_2="" >
									</span>
								</a>
							</li>
							<li name="li_name">
								<a id="4" href="/cms/channel/1ywgg1/29607.htm" title="青海西矿稀贵金属有限公司氧化锌销售项目（第三次）公开竞价销售公告  招标公告"  target="_blank" rel="noopener noreferrer" style="">
									<span>
										<i class="iconfont">&#xe638;</i>
										<em style="width:6.5em; color: #1e52a8;font-weight: 700;float: none;">
											
												<!-- 采购方式 -->

										</em>
										青海西矿稀贵金属有限公司氧化锌销售项目（第三次）公开竞价销售公告
									</span>
									<em>
											2025-06-27
									</em>
									<!-- 状态 -->
									<span class="style">
										<input type="hidden" bmstart_1="2025-06-27 16:49:00" bmend_1="2025-06-27 16:49:00"
												buystart_1="2025-06-27 12:00:00" buyend_1="2025-07-03 18:00:00" class="endTime1" 
												bmstart_2="" bmend_2=""
												buystart_2="" buyend_2="" >
									</span>
								</a>
							</li>
							<li name="li_name">
								<a id="5" href="/cms/channel/1ywgg1/29606.htm" title="内蒙古双利矿业有限公司二号铁矿（改、扩建）工程采购井口空气预热设备采购项目招标公告  招标公告"  target="_blank" rel="noopener noreferrer" style="">
									<span>
										<i class="iconfont">&#xe638;</i>
										<em style="width:6.5em; color: #1e52a8;font-weight: 700;float: none;">
											
												<!-- 采购方式 -->

										</em>
										内蒙古双利矿业有限公司二号铁矿（改、扩建）工程采购井口空气预热设备采购项目招标公告
									</span>
									<em>
											2025-06-27
									</em>
									<!-- 状态 -->
									<span class="style">
										<input type="hidden" bmstart_1="2025-06-27 16:46:00" bmend_1="2025-06-27 16:46:00"
												buystart_1="2025-06-28 08:30:00" buyend_1="2025-07-03 18:00:00" class="endTime1" 
												bmstart_2="" bmend_2=""
												buystart_2="" buyend_2="" >
									</span>
								</a>
							</li>
							<li name="li_name">
								<a id="6" href="/cms/channel/1ywgg1/29605.htm" title="四川鑫源矿业有限责任公司2025年钢球采购项目招标公告  招标公告"  target="_blank" rel="noopener noreferrer" style="">
									<span>
										<i class="iconfont">&#xe638;</i>
										<em style="width:6.5em; color: #1e52a8;font-weight: 700;float: none;">
											
												<!-- 采购方式 -->

										</em>
										四川鑫源矿业有限责任公司2025年钢球采购项目招标公告
									</span>
									<em>
											2025-06-27
									</em>
									<!-- 状态 -->
									<span class="style">
										<input type="hidden" bmstart_1="2025-06-27 16:31:00" bmend_1="2025-06-27 16:31:00"
												buystart_1="2025-06-28 08:30:00" buyend_1="2025-07-03 18:00:00" class="endTime1" 
												bmstart_2="" bmend_2=""
												buystart_2="" buyend_2="" >
									</span>
								</a>
							</li>
							<li name="li_name">
								<a id="7" href="/cms/channel/1ywgg1/29585.htm" title="四川鑫源矿业有限责任公司80万吨年选矿厂提升改造项目自动化建设大型分析仪系统采购项目招标公告  招标公告"  target="_blank" rel="noopener noreferrer" style="">
									<span>
										<i class="iconfont">&#xe638;</i>
										<em style="width:6.5em; color: #1e52a8;font-weight: 700;float: none;">
											
												<!-- 采购方式 -->

										</em>
										四川鑫源矿业有限责任公司80万吨年选矿厂提升改造项目自动化建设大型分析仪系统采购项目招标公告
									</span>
									<em>
											2025-06-26
									</em>
									<!-- 状态 -->
									<span class="style">
										<input type="hidden" bmstart_1="2025-06-26 20:36:00" bmend_1="2025-06-26 20:36:00"
												buystart_1="2025-06-27 00:00:00" buyend_1="2025-07-02 23:59:00" class="endTime1" 
												bmstart_2="" bmend_2=""
												buystart_2="" buyend_2="" >
									</span>
								</a>
							</li>
							<li name="li_name">
								<a id="8" href="/cms/channel/1ywgg1/29549.htm" title="青海西矿杭萧钢构有限公司2025年废钢销售项目销售公告  招标公告"  target="_blank" rel="noopener noreferrer" style="">
									<span>
										<i class="iconfont">&#xe638;</i>
										<em style="width:6.5em; color: #1e52a8;font-weight: 700;float: none;">
											
												<!-- 采购方式 -->

										</em>
										青海西矿杭萧钢构有限公司2025年废钢销售项目销售公告
									</span>
									<em>
											2025-06-25
									</em>
									<!-- 状态 -->
									<span class="style">
										<input type="hidden" bmstart_1="2025-06-25 17:34:00" bmend_1="2025-07-02 08:12:00"
												buystart_1="2025-06-26 08:30:00" buyend_1="2025-07-02 08:12:00" class="endTime1" 
												bmstart_2="" bmend_2=""
												buystart_2="" buyend_2="" >
									</span>
								</a>
							</li>
							<li name="li_name">
								<a id="9" href="/cms/channel/1ywgg1/29546.htm" title="西藏玉龙铜业股份有限公司新建尾矿输送自流槽桥架南侧边坡治理工程【重新招标】招标公告  招标公告"  target="_blank" rel="noopener noreferrer" style="">
									<span>
										<i class="iconfont">&#xe638;</i>
										<em style="width:6.5em; color: #1e52a8;font-weight: 700;float: none;">
											
												<!-- 采购方式 -->

										</em>
										西藏玉龙铜业股份有限公司新建尾矿输送自流槽桥架南侧边坡治理工程【重新招标】招标公告
									</span>
									<em>
											2025-06-25
									</em>
									<!-- 状态 -->
									<span class="style">
										<input type="hidden" bmstart_1="2025-06-25 16:57:00" bmend_1="2025-06-25 16:57:00"
												buystart_1="2025-06-26 00:00:00" buyend_1="2025-06-30 23:59:00" class="endTime1" 
												bmstart_2="" bmend_2=""
												buystart_2="" buyend_2="" >
									</span>
								</a>
							</li>
						
				</ul>
			</div>
				<!--分页 -->
					<ul>
<div class="pagination">
    <div class="pag-txt">
        当前<em>1</em>页 / 共计<em>169</em>页</div>
    <div class="pag-link">
        <a disabled="disabled">首页</a>
    <a href="javascript:;" class="disabled"  aria-label="Previous" page="0">上页</a>
    <a href="javascript:;" class="pag-cur">1</a>







<a href="javascript:;" class="pageItem"  page="2">2</a>
<a href="javascript:;" class="pageItem"  page="3">3</a>
<a href="javascript:;">...</a>

<a href="javascript:;" class="pageItem"   page="169">169</a>
<a href="javascript:;" aria-label="Next" class="pageItem" page="2">下页</a>

<a onclick="page(169)")>尾页</a>
<span class="jump"><em>跳转到</em><input type="text" id="page" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"><em>页</em>
					<a href="###" id="jumpPage">GO</a>
				</span>
</div>
</div>

					</ul>
				<!--分页-end -->
        </div>
			<!--  信息列表分页-END -->
'''

def test_mining_site_parsing():
    """测试矿业网站解析"""
    print("开始测试矿业招标网站解析...")

    # 创建解析器
    parser = HTMLParser()

    # 获取配置
    config = SELECTOR_CONFIGS['mining_bidding']
    print(f"使用配置: {config}")

    # 先测试选择器是否能找到元素
    soup = parser.parse_html(html_content)
    items = soup.select(config['item_selector'])
    print(f"找到 {len(items)} 个li元素")

    for i, item in enumerate(items):
        print(f"\n第 {i+1} 个li元素:")
        title_element = item.select_one(config['title_selector'])
        if title_element:
            print(f"  找到a标签: {title_element.get('title', 'NO_TITLE')}")
            print(f"  href: {title_element.get('href', 'NO_HREF')}")

            # 查看a标签内的所有em元素
            em_elements = title_element.select('em')
            print(f"  a标签内有 {len(em_elements)} 个em元素:")
            for j, em in enumerate(em_elements):
                print(f"    em[{j}]: '{em.get_text(strip=True)}'")
        else:
            print("  未找到a标签")

        date_element = item.select_one(config['date_selector'])
        if date_element:
            print(f"  找到日期元素: '{date_element.get_text(strip=True)}'")
        else:
            print("  未找到日期元素")
            # 尝试其他选择器
            alt_selectors = ['em', 'a > em', 'em:nth-child(2)', 'em:nth-of-type(2)']
            for selector in alt_selectors:
                alt_element = item.select_one(selector)
                if alt_element:
                    text = alt_element.get_text(strip=True)
                    if text and text != '':
                        print(f"    备选选择器 '{selector}': '{text}'")

    # 解析HTML
    bidding_list = parser.extract_bidding_info(html_content, config)
    
    print(f"\n解析结果:")
    print(f"共解析到 {len(bidding_list)} 条招标信息")
    
    for i, bidding in enumerate(bidding_list, 1):
        print(f"\n第 {i} 条:")
        print(f"  标题: {bidding.title}")
        print(f"  链接: {bidding.link}")
        print(f"  日期: {bidding.publish_date}")
        print(f"  分类: {bidding.category}")
        print(f"  地区: {bidding.location}")
    
    # 测试今日数据过滤
    print(f"\n测试今日数据过滤...")
    today_bidding = parser.filter_today_data(bidding_list)
    print(f"今日数据: {len(today_bidding)} 条")

    for bidding in today_bidding:
        print(f"  今日: {bidding.title} ({bidding.publish_date})")

    # 测试消息格式化
    print(f"\n测试消息格式化...")
    from src.formatter import MessageFormatter
    formatter = MessageFormatter()

    # 格式化所有数据（模拟推送效果）
    markdown_message = formatter.format_bidding_list_markdown(bidding_list, "矿业招标信息测试")
    print("Markdown格式消息:")
    print("=" * 50)
    print(markdown_message)
    print("=" * 50)

if __name__ == '__main__':
    test_mining_site_parsing()
