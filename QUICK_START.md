# 快速开始指南

## 针对您的矿业招标网站

根据您提供的HTML结构，我已经为您的网站创建了专门的配置。

### 1. 配置环境

复制配置模板：
```bash
cp config.env.template .env
```

编辑 `.env` 文件，填写以下信息：
```env
# 企业微信配置（必填）
CORP_ID=your_corp_id_here
CORP_SECRET=your_corp_secret_here  
AGENT_ID=your_agent_id_here

# 招标网站配置
TARGET_URL=https://your-mining-site.com/bidding-list
REQUEST_DELAY=2
MAX_RETRIES=3

# 消息推送配置
TO_USER=@all

# 定时任务配置
SCHEDULE_TIME=09:00
```

### 2. 测试系统

```bash
# 测试所有组件（包括企业微信连接）
python main.py --config mining_site --test
```

### 3. 手动执行一次

```bash
# 执行一次完整的抓取推送流程
python main.py --config mining_site --once
```

### 4. 启动定时任务

```bash
# 启动定时任务，每天自动执行
python main.py --config mining_site
```

## 预期效果

系统将会：

1. **自动抓取**您网站上的招标信息
2. **智能解析**标题、链接、发布日期
3. **过滤今日**发布的新信息
4. **格式化消息**为精美的Markdown格式
5. **推送到企业微信**群组或个人

### 消息示例

```markdown
# 📋 2025年07月03日 招标信息汇总

📅 **统计时间**: 2025-07-03 09:00:00
📊 **信息数量**: 3 条

---

## 1. 青海西矿杭萧钢构有限公司2025年废钢销售项目【重新招标】销售公告
📅 **发布时间**: 2025-07-02
🔗 **详情链接**: [点击查看](https://your-site.com/detail/1)

## 2. 西藏玉龙铜业股份有限公司2025年硫化钠零库存采购项目
📅 **发布时间**: 2025-07-01
🔗 **详情链接**: [点击查看](https://your-site.com/detail/2)

---
🤖 *由AutoWebsite自动推送 | 09:00:15*
```

## 故障排除

### 1. 企业微信配置问题

如果收不到消息，请检查：
- 企业ID、Secret、AgentID是否正确
- 应用可见范围是否包含目标用户
- 网络连接是否正常

### 2. 网页抓取问题

如果抓取不到数据，请检查：
- TARGET_URL是否正确
- 网站是否需要登录
- 是否被反爬机制拦截

### 3. 数据解析问题

如果解析不到信息，可能是：
- 网站HTML结构发生变化
- 需要调整选择器配置

## 高级配置

### 自定义消息格式

可以修改 `src/formatter.py` 中的格式化逻辑来自定义消息样式。

### 添加更多字段

如果需要提取更多信息（如项目金额、截止时间等），可以在配置中添加相应的选择器。

### 多网站支持

可以为不同的网站创建不同的配置，然后分别运行多个实例。

## 技术支持

如果遇到问题，请：

1. 查看日志文件 `logs/app.log`
2. 运行测试模式检查各组件状态
3. 检查网站HTML结构是否变化
4. 验证企业微信配置是否正确

系统已经针对您的网站结构进行了优化，应该可以直接使用！
