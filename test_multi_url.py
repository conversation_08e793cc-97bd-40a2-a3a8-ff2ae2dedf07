#!/usr/bin/env python3
"""
测试多URL功能
"""
import os
from src.config import Config
from src.multi_url_scheduler import MultiUrlBiddingBot
from src.advanced_scheduler import AdvancedBiddingBot

def test_multi_url_simple():
    """测试简单多URL模式"""
    print("🧪 测试简单多URL模式...")
    
    # 模拟多URL配置
    test_urls = [
        "http://mn.southmn.com/list/?368_1.html",
        "https://ec.westmining.com/cms/channel/1ywgg1/index.htm",     
    ]
    
    # 临时设置环境变量
    os.environ['TARGET_URLS'] = ','.join(test_urls)
    
    # 重新加载配置
    Config.TARGET_URLS = test_urls
    
    print(f"配置的URL数量: {len(Config.get_target_urls())}")
    for i, url in enumerate(Config.get_target_urls(), 1):
        print(f"  {i}. {url}")
    
    # 创建机器人实例（不需要企业微信配置）
    try:
        bot = MultiUrlBiddingBot('mining_site')
        
        # 测试抓取（不发送消息）
        print("\n📡 开始测试抓取...")
        
        target_urls = Config.get_target_urls()
        all_bidding_list = []
        
        for i, url in enumerate(target_urls, 1):
            print(f"\n抓取第 {i}/{len(target_urls)} 个网页: {url}")
            
            html_content = bot.scraper.fetch_page(url)
            if html_content:
                bidding_list = bot.parser.extract_bidding_info(html_content, bot.selector_config)
                print(f"  ✅ 解析到 {len(bidding_list)} 条信息")
                all_bidding_list.extend(bidding_list)
                
                # 显示前2条
                for j, bidding in enumerate(bidding_list[:2], 1):
                    print(f"    {j}. {bidding.title} ({bidding.publish_date})")
            else:
                print(f"  ❌ 抓取失败")
        
        print(f"\n📊 总计解析到 {len(all_bidding_list)} 条信息")
        
        # 去重测试
        unique_list = bot._remove_duplicates(all_bidding_list)
        print(f"📋 去重后剩余 {len(unique_list)} 条信息")
        
        # 今日数据过滤
        today_bidding = bot.parser.filter_today_data(unique_list)
        print(f"📅 今日数据 {len(today_bidding)} 条")
        
        # 消息格式化测试
        message = bot.formatter.format_bidding_list_markdown(
            unique_list[:3], 
            "多URL测试消息"
        )
        print(f"\n💬 消息格式化完成，长度: {len(message)} 字符")
        
        bot.scraper.close()
        
        print("\n✅ 简单多URL模式测试成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 简单多URL模式测试失败: {e}")
        return False

def test_multi_url_advanced():
    """测试高级多URL模式"""
    print("\n🧪 测试高级多URL模式...")
    
    # 模拟多URL配置
    test_urls = [
        "http://mn.southmn.com/list/?368_1.html",
        "https://ec.westmining.com/cms/channel/1ywgg1/index.htm",  
    ]
    
    # 临时设置环境变量
    os.environ['TARGET_URLS'] = ','.join(test_urls)
    Config.TARGET_URLS = test_urls
    
    try:
        # 创建高级机器人实例
        bot = AdvancedBiddingBot()
        
        print(f"配置的URL数量: {len(Config.get_target_urls())}")
        
        # 测试配置映射
        for url in Config.get_target_urls():
            config_name = bot._get_config_for_url(url)
            print(f"  {url} → {config_name}")
        
        # 测试抓取
        print("\n📡 开始测试高级抓取...")
        
        target_urls = Config.get_target_urls()
        all_bidding_list = []
        
        for i, url in enumerate(target_urls, 1):
            print(f"\n抓取第 {i}/{len(target_urls)} 个网页: {url}")
            
            config_name = bot._get_config_for_url(url)
            print(f"  使用配置: {config_name}")
            
            html_content = bot.scraper.fetch_page(url)
            if html_content:
                from src.parser import SELECTOR_CONFIGS
                config = SELECTOR_CONFIGS.get(config_name, SELECTOR_CONFIGS['default'])
                bidding_list = bot.parser.extract_bidding_info(html_content, config)
                print(f"  ✅ 解析到 {len(bidding_list)} 条信息")
                all_bidding_list.extend(bidding_list)
            else:
                print(f"  ❌ 抓取失败")
        
        print(f"\n📊 总计解析到 {len(all_bidding_list)} 条信息")
        
        bot.scraper.close()
        
        print("\n✅ 高级多URL模式测试成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 高级多URL模式测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始多URL功能测试\n")
    
    # 确保配置存在
    from config_mining_site import setup_mining_site_config
    setup_mining_site_config()
    
    # 设置多网站配置
    from src.multi_site_configs import setup_multi_site_configs
    setup_multi_site_configs()
    
    success1 = test_multi_url_simple()
    success2 = test_multi_url_advanced()
    
    print(f"\n📋 测试结果:")
    print(f"  简单多URL模式: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"  高级多URL模式: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print(f"\n🎉 所有测试通过！")
        print(f"\n📋 使用方法:")
        print(f"1. 配置多个URL到 .env 文件")
        print(f"2. 运行: python main_multi_url.py --test --mode simple")
        print(f"3. 运行: python main_multi_url.py --test --mode advanced")
        print(f"4. 选择合适的模式启动: python main_multi_url.py --mode [simple|advanced]")
    else:
        print(f"\n❌ 部分测试失败，请检查配置")

if __name__ == '__main__':
    main()
