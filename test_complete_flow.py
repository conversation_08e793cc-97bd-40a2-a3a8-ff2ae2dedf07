#!/usr/bin/env python3
"""
测试完整的系统流程（不包括企业微信推送）
"""
import os
from src.scraper import WebScraper
from src.parser import HTMLParser, SELECTOR_CONFIGS
from src.formatter import MessageFormatter

def test_complete_flow():
    """测试完整的系统流程"""
    print("🚀 开始测试完整系统流程...")
    
    # 设置配置
    url = "https://ec.westmining.com/cms/channel/1ywgg1/index.htm"
    config_name = 'mining_site'
    
    # 确保配置存在
    if config_name not in SELECTOR_CONFIGS:
        print(f"❌ 配置 '{config_name}' 不存在")
        return False
    
    config = SELECTOR_CONFIGS[config_name]
    print(f"✅ 使用配置: {config_name}")
    
    try:
        # 1. 网页抓取
        print("\n📡 步骤1: 抓取网页...")
        scraper = WebScraper()
        html_content = scraper.fetch_page(url)
        
        if not html_content:
            print("❌ 网页抓取失败")
            return False
        
        print(f"✅ 网页抓取成功，内容长度: {len(html_content)}")
        
        # 2. 数据解析
        print("\n🔍 步骤2: 解析招标信息...")
        parser = HTMLParser()
        bidding_list = parser.extract_bidding_info(html_content, config)
        
        if not bidding_list:
            print("❌ 未解析到招标信息")
            return False
        
        print(f"✅ 成功解析 {len(bidding_list)} 条招标信息")
        
        # 显示前3条
        for i, bidding in enumerate(bidding_list[:3], 1):
            print(f"  {i}. {bidding.title} ({bidding.publish_date})")
        
        # 3. 过滤今日数据
        print("\n📅 步骤3: 过滤今日数据...")
        today_bidding = parser.filter_today_data(bidding_list)
        
        if today_bidding:
            print(f"✅ 找到 {len(today_bidding)} 条今日招标信息")
            for bidding in today_bidding:
                print(f"  今日: {bidding.title}")
        else:
            print("ℹ️ 今日暂无新的招标信息")
        
        # 4. 消息格式化
        print("\n💬 步骤4: 格式化消息...")
        formatter = MessageFormatter()
        
        # 根据是否有今日数据选择格式化内容
        if today_bidding:
            message = formatter.format_bidding_list_markdown(today_bidding, "今日招标信息")
            print("✅ 今日招标信息格式化完成")
        else:
            # 如果没有今日数据，格式化最新的3条作为示例
            recent_bidding = bidding_list[:3]
            message = formatter.format_bidding_list_markdown(recent_bidding, "最新招标信息")
            print("✅ 最新招标信息格式化完成")
        
        # 5. 保存消息到文件
        print("\n💾 步骤5: 保存消息...")
        with open('formatted_message.md', 'w', encoding='utf-8') as f:
            f.write(message)
        print("✅ 格式化消息已保存到 formatted_message.md")
        
        # 6. 显示消息预览
        print("\n📋 消息预览:")
        print("=" * 60)
        print(message[:500] + "..." if len(message) > 500 else message)
        print("=" * 60)
        
        # 7. 统计信息
        print(f"\n📊 统计信息:")
        print(f"  总招标信息: {len(bidding_list)} 条")
        print(f"  今日信息: {len(today_bidding)} 条")
        print(f"  消息长度: {len(message)} 字符")
        
        # 8. 验证链接
        print(f"\n🔗 链接验证:")
        valid_links = 0
        for bidding in bidding_list[:3]:
            if bidding.link.startswith('https://'):
                valid_links += 1
                print(f"  ✅ {bidding.link}")
            else:
                print(f"  ❌ {bidding.link}")
        
        print(f"  有效链接: {valid_links}/{min(3, len(bidding_list))}")
        
        scraper.close()
        
        print(f"\n🎉 完整系统流程测试成功！")
        print(f"\n📋 下一步:")
        print(f"1. 配置企业微信参数到 .env 文件")
        print(f"2. 运行: python main.py --config mining_site --test")
        print(f"3. 运行: python main.py --config mining_site --once")
        print(f"4. 启动定时任务: python main.py --config mining_site")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

if __name__ == '__main__':
    success = test_complete_flow()
    if not success:
        print("\n❌ 系统测试失败，请检查配置和网络连接")
