# AutoWebsite - 招标信息自动抓取推送系统

一个自动抓取招标网站信息并推送到企业微信的Python应用程序。

## 功能特性

- 🕷️ **智能网页抓取**: 支持反爬机制处理，自动重试和延时
- 📊 **数据解析**: 使用BeautifulSoup解析HTML，提取招标信息
- 📅 **日期过滤**: 自动过滤今日发布的招标信息
- 💬 **企业微信推送**: 支持文本、Markdown、图文等多种消息格式
- ⏰ **定时任务**: 支持定时自动执行，可配置执行时间
- 🔧 **灵活配置**: 支持多种网站的选择器配置
- 📝 **完整日志**: 详细的日志记录和错误处理
- 🧪 **测试支持**: 包含完整的单元测试

## 项目结构

```
AutoWebsite/
├── src/                    # 源代码目录
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── scraper.py         # 网页抓取模块
│   ├── parser.py          # 数据解析模块
│   ├── wechat_api.py      # 企业微信API模块
│   ├── formatter.py       # 消息格式化模块
│   └── scheduler.py       # 定时任务模块
├── tests/                 # 测试目录
│   ├── __init__.py
│   ├── test_parser.py
│   └── test_formatter.py
├── logs/                  # 日志目录（自动创建）
├── main.py               # 主程序入口
├── requirements.txt      # 依赖包列表
├── config.env.template   # 配置文件模板
└── README.md            # 项目说明
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制配置模板并填写实际配置：

```bash
cp config.env.template .env
```

编辑 `.env` 文件，填写以下配置：

```env
# 企业微信配置
CORP_ID=your_corp_id_here
CORP_SECRET=your_corp_secret_here  
AGENT_ID=your_agent_id_here

# 招标网站配置
TARGET_URL=https://your-target-site.com
REQUEST_DELAY=2
MAX_RETRIES=3

# 消息推送配置
TO_USER=@all
TO_PARTY=
TO_TAG=

# 定时任务配置
SCHEDULE_TIME=09:00
```

### 3. 企业微信配置说明

1. **获取企业ID (CORP_ID)**:
   - 登录企业微信管理后台
   - 在"我的企业" -> "企业信息"中查看企业ID

2. **创建应用获取Secret和AgentID**:
   - 在"应用管理"中创建自建应用
   - 获取应用的Secret (CORP_SECRET)
   - 获取应用的AgentId (AGENT_ID)

3. **设置可见范围**:
   - 在应用设置中配置可见范围
   - 确保目标用户能够接收消息

## 使用方法

### 测试模式

运行系统测试，检查所有组件是否正常：

```bash
python main.py --test
```

### 执行一次任务

手动执行一次完整的抓取推送流程：

```bash
python main.py --once
```

### 启动定时任务

启动定时任务，按配置的时间自动执行：

```bash
python main.py
```

### 使用自定义配置

```bash
# 使用矿业网站配置
python main.py --config mining_site

# 使用其他自定义配置
python main.py --config example_site
```

## 自定义网站配置

### 内置配置

系统已内置以下网站配置：

- `default`: 通用表格型网站配置
- `mining_site`: 矿业招标网站配置（支持li列表结构）

### 矿业招标网站使用

如果您的网站HTML结构类似以下格式：

```html
<ul id="list1">
    <li name="li_name">
        <a href="/detail/1" title="招标项目标题">
            <span>项目内容</span>
            <em>2025-07-02</em>
        </a>
    </li>
</ul>
```

请使用 `mining_site` 配置：

```bash
# 测试矿业网站配置
python main.py --config mining_site --test

# 使用矿业网站配置执行任务
python main.py --config mining_site --once
```

### 添加新的网站配置

在 `src/parser.py` 的 `SELECTOR_CONFIGS` 中添加新的网站配置：

```python
SELECTOR_CONFIGS = {
    'your_site': {
        'item_selector': '.bidding-item',      # 招标项目容器选择器
        'title_selector': '.title a',          # 标题链接选择器
        'date_selector': '.publish-date',      # 发布日期选择器
        'category_selector': '.category',      # 分类选择器
        'location_selector': '.location',      # 地区选择器
        'content_selector': '.summary',        # 内容摘要选择器
        'base_url': 'https://your-site.com'   # 基础URL
    }
}
```

## 运行测试

运行单元测试：

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试文件
python -m pytest tests/test_parser.py

# 运行测试并显示覆盖率
python -m pytest tests/ --cov=src
```

或使用unittest：

```bash
python -m unittest discover tests
```

## 日志管理

日志文件位于 `logs/` 目录：
- 自动按天轮转
- 保留30天历史日志
- 支持不同日志级别

查看日志：

```bash
tail -f logs/app.log
```

## 消息格式示例

### Markdown格式消息

```markdown
# 📋 2024年01月15日 招标信息汇总

📅 **统计时间**: 2024-01-15 09:00:00
📊 **信息数量**: 3 条

---

## 1. XX市政工程建设项目招标公告
📅 **发布时间**: 2024-01-15
🏷️ **分类**: 工程建设
📍 **地区**: 北京市
📝 **摘要**: 项目总投资约500万元...
🔗 **详情链接**: [点击查看](https://example.com/1)

---
🤖 *由AutoWebsite自动推送 | 09:00:15*
```

## 错误处理

系统包含完善的错误处理机制：

- **网络错误**: 自动重试，支持代理设置
- **解析错误**: 记录详细日志，继续处理其他数据
- **推送失败**: 发送错误通知，记录失败原因
- **配置错误**: 启动时验证配置，提供详细错误信息

## 性能优化

- 使用连接池复用HTTP连接
- 智能延时避免被反爬
- 消息长度自动截断
- 内存使用优化

## 安全考虑

- 敏感配置使用环境变量
- 请求头伪装避免被识别
- 访问频率控制
- 错误信息脱敏

## 常见问题

### 1. 企业微信消息发送失败

- 检查企业ID、Secret、AgentID是否正确
- 确认应用可见范围包含目标用户
- 检查网络连接是否正常

### 2. 网页抓取失败

- 检查目标URL是否可访问
- 调整请求延时和重试次数
- 检查是否需要登录或验证码

### 3. 数据解析为空

- 检查选择器配置是否正确
- 查看目标网站HTML结构是否变化
- 使用浏览器开发者工具调试选择器

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue或联系开发者。
