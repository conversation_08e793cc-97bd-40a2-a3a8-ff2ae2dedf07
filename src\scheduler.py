"""
定时任务模块
"""
import schedule
import time
from datetime import datetime
from loguru import logger
from .config import Config, setup_logging
from .scraper import WebScraper
from .parser import HTMLParser, SELECTOR_CONFIGS
from .wechat_api import WeChatAPI
from .formatter import MessageFormatter


class BiddingBot:
    """招标信息推送机器人"""
    
    def __init__(self, selector_config_name: str = 'default'):
        """
        初始化机器人
        
        Args:
            selector_config_name (str): 选择器配置名称
        """
        # 设置日志
        setup_logging()
        
        # 验证配置
        Config.validate()
        
        # 初始化组件
        self.scraper = WebScraper()
        self.parser = HTMLParser()
        self.wechat_api = WeChatAPI()
        self.formatter = MessageFormatter()
        
        # 获取选择器配置
        self.selector_config = SELECTOR_CONFIGS.get(
            selector_config_name, 
            SELECTOR_CONFIGS['default']
        )
        
        logger.info("招标信息推送机器人初始化完成")
    
    def run_once(self) -> bool:
        """
        执行一次完整的抓取和推送流程
        
        Returns:
            bool: 执行是否成功
        """
        try:
            logger.info("开始执行招标信息抓取推送任务")
            
            # 1. 抓取网页数据
            html_content = self.scraper.fetch_page(Config.TARGET_URL)
            if not html_content:
                logger.error("网页抓取失败")
                return False
            
            # 2. 解析招标信息
            bidding_list = self.parser.extract_bidding_info(html_content, self.selector_config)
            if not bidding_list:
                logger.warning("未解析到招标信息")
                # 发送空消息通知
                empty_message = self.formatter.format_bidding_list_markdown([])
                self.wechat_api.send_markdown_message(empty_message)
                return True
            for bidding in bidding_list:
                logger.info(f"解析到招标信息: {bidding}")
            # logger.info(f"准备推送最近的1条今日招标信息{bidding_list[1]}")
            # 3. 过滤今日数据
            today_bidding = self.parser.filter_today_data(bidding_list)
            
            # 4. 格式化消息
            if today_bidding:
                message = self.formatter.format_bidding_list_markdown(today_bidding)
                logger.info(f"准备推送 {len(today_bidding)} 条今日招标信息")
            else:
                message = self.formatter.format_bidding_list_markdown([])
                logger.info("今日无新招标信息，发送空消息通知")
            
            # 5. 发送消息
            success = self.wechat_api.send_markdown_message(message)
            
            if success:
                logger.info("招标信息推送完成")
                return True
            else:
                logger.error("消息发送失败")
                return False
                
        except Exception as e:
            logger.error(f"执行任务时发生错误: {e}")
            # 发送错误通知
            error_message = f"⚠️ 招标信息推送任务执行失败\n\n错误信息: {str(e)}\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            try:
                self.wechat_api.send_text_message(error_message)
            except:
                pass
            return False
    
    def test_all_components(self) -> bool:
        """测试所有组件"""
        logger.info("开始测试所有组件")
        
        try:
            # 测试企业微信连接
            logger.info("测试企业微信连接...")
            if not self.wechat_api.test_connection():
                logger.error("企业微信连接测试失败")
                return False
            
            # 测试网页抓取
            logger.info("测试网页抓取...")
            html_content = self.scraper.fetch_page(Config.TARGET_URL)
            if not html_content:
                logger.error("网页抓取测试失败")
                return False
            
            # 测试数据解析
            logger.info("测试数据解析...")
            bidding_list = self.parser.extract_bidding_info(html_content, self.selector_config)
            logger.info(f"解析到 {len(bidding_list)} 条数据")
            
            # 发送测试消息
            logger.info("发送测试消息...")
            test_message = f"""# 🧪 系统测试报告

📅 **测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 测试结果
✅ **企业微信连接**: 正常
✅ **网页抓取**: 正常
✅ **数据解析**: 正常 (解析到 {len(bidding_list)} 条数据)

---
🤖 *AutoWebsite系统测试完成*"""
            
            success = self.wechat_api.send_markdown_message(test_message)
            
            if success:
                logger.info("所有组件测试通过")
                return True
            else:
                logger.error("测试消息发送失败")
                return False
                
        except Exception as e:
            logger.error(f"组件测试时发生错误: {e}")
            return False
    
    def start_scheduler(self):
        """启动定时任务"""
        logger.info(f"启动定时任务，执行时间: {Config.SCHEDULE_TIME}")
        
        # 设置定时任务
        schedule.every().day.at(Config.SCHEDULE_TIME).do(self.run_once)
        
        # 发送启动通知
        start_message = f"""# 🚀 招标信息推送服务启动

📅 **启动时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
⏰ **推送时间**: 每天 {Config.SCHEDULE_TIME}
🎯 **目标网站**: {Config.TARGET_URL}

---
🤖 *服务已启动，将自动推送招标信息*"""
        
        try:
            self.wechat_api.send_markdown_message(start_message)
        except Exception as e:
            logger.warning(f"启动通知发送失败: {e}")
        
        # 运行调度器
        logger.info("定时任务调度器开始运行")
        while True:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except KeyboardInterrupt:
                logger.info("收到停止信号，正在关闭...")
                break
            except Exception as e:
                logger.error(f"调度器运行时发生错误: {e}")
                time.sleep(300)  # 出错后等待5分钟再继续
    
    def stop(self):
        """停止服务"""
        logger.info("正在停止服务...")
        
        # 关闭网络会话
        if hasattr(self, 'scraper'):
            self.scraper.close()
        
        # 发送停止通知
        stop_message = f"""# 🛑 招标信息推送服务停止

📅 **停止时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

---
🤖 *服务已停止*"""
        
        try:
            self.wechat_api.send_markdown_message(stop_message)
        except Exception as e:
            logger.warning(f"停止通知发送失败: {e}")
        
        logger.info("服务已停止")


def main():
    """主函数"""
    import sys
    import argparse
    
    parser = argparse.ArgumentParser(description='招标信息自动抓取推送系统')
    parser.add_argument('--test', action='store_true', help='运行测试模式')
    parser.add_argument('--once', action='store_true', help='执行一次任务')
    parser.add_argument('--config', default='default', help='选择器配置名称')
    
    args = parser.parse_args()
    
    try:
        bot = BiddingBot(args.config)
        
        if args.test:
            # 测试模式
            success = bot.test_all_components()
            sys.exit(0 if success else 1)
        elif args.once:
            # 执行一次
            success = bot.run_once()
            sys.exit(0 if success else 1)
        else:
            # 定时任务模式
            bot.start_scheduler()
            
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序运行时发生错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
