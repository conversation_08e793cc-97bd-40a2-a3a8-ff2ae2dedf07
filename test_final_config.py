#!/usr/bin/env python3
"""
测试最终推荐配置
"""
import os
from src.config import Config
from src.advanced_scheduler import AdvancedBiddingBot
from src.multi_site_configs import setup_multi_site_configs

def test_final_config():
    """测试最终推荐配置"""
    print("🚀 测试最终推荐配置")
    print("=" * 60)
    
    # 推荐的URL配置
    recommended_urls = [
        "http://mn.southmn.com/list/?368_1.html",           # 南方锰业 - 普通配置
        "https://ec.westmining.com/cms/channel/1ywgg1/index.htm",  # 西矿电商 - mining_site配置
    ]
    
    print("📋 推荐的URL配置:")
    for i, url in enumerate(recommended_urls, 1):
        print(f"  {i}. {url}")
    
    # 设置配置
    os.environ['TARGET_URLS'] = ','.join(recommended_urls)
    Config.TARGET_URLS = recommended_urls
    
    # 设置多网站配置
    setup_multi_site_configs()
    
    try:
        # 创建高级机器人实例
        bot = AdvancedBiddingBot()
        
        print(f"\n🔧 配置映射:")
        for url in recommended_urls:
            config_name = bot._get_config_for_url(url)
            print(f"  {url}")
            print(f"  → {config_name}")
            print()
        
        # 测试抓取
        print(f"📡 开始测试抓取...")
        
        all_bidding_list = []
        success_count = 0
        
        for i, url in enumerate(recommended_urls, 1):
            print(f"\n抓取第 {i}/{len(recommended_urls)} 个网页:")
            print(f"URL: {url}")
            
            try:
                config_name = bot._get_config_for_url(url)
                print(f"使用配置: {config_name}")
                
                html_content = bot.scraper.fetch_page(url)
                if html_content:
                    from src.parser import SELECTOR_CONFIGS
                    config = SELECTOR_CONFIGS.get(config_name, SELECTOR_CONFIGS['default'])
                    bidding_list = bot.parser.extract_bidding_info(html_content, config)
                    
                    if bidding_list:
                        print(f"✅ 解析到 {len(bidding_list)} 条信息")
                        success_count += 1
                        
                        # 显示前2条
                        print(f"前2条信息:")
                        for j, bidding in enumerate(bidding_list[:2], 1):
                            print(f"  {j}. {bidding.title}")
                            print(f"     日期: {bidding.publish_date}")
                            print(f"     链接: {bidding.link}")
                        
                        all_bidding_list.extend(bidding_list)
                    else:
                        print(f"❌ 未解析到信息")
                else:
                    print(f"❌ 抓取失败")
                    
            except Exception as e:
                print(f"❌ 处理失败: {e}")
        
        print(f"\n📊 测试结果:")
        print(f"  成功网站: {success_count}/{len(recommended_urls)}")
        print(f"  总计信息: {len(all_bidding_list)} 条")
        
        if all_bidding_list:
            # 去重
            unique_list = bot._remove_duplicates(all_bidding_list)
            print(f"  去重后: {len(unique_list)} 条")
            
            # 今日数据
            today_bidding = bot.parser.filter_today_data(unique_list)
            print(f"  今日数据: {len(today_bidding)} 条")
            
            # 格式化消息
            if today_bidding:
                message = bot.formatter.format_bidding_list_markdown(
                    today_bidding, 
                    "多网站今日招标信息"
                )
                print(f"  今日消息长度: {len(message)} 字符")
            else:
                message = bot.formatter.format_bidding_list_markdown(
                    unique_list[:5], 
                    "多网站最新招标信息"
                )
                print(f"  最新消息长度: {len(message)} 字符")
            
            # 保存消息
            with open('final_test_message.md', 'w', encoding='utf-8') as f:
                f.write(message)
            print(f"  💾 消息已保存到: final_test_message.md")
        
        bot.scraper.close()
        
        # 判断测试结果
        if success_count == len(recommended_urls):
            print(f"\n🎉 所有网站测试成功！")
            print(f"\n📋 使用方法:")
            print(f"1. 复制推荐配置:")
            print(f"   cp config.env.recommended_urls .env")
            print(f"2. 编辑企业微信配置")
            print(f"3. 测试系统:")
            print(f"   python main_multi_url.py --test --mode advanced")
            print(f"4. 启动定时任务:")
            print(f"   python main_multi_url.py --mode advanced")
            return True
        else:
            print(f"\n⚠️ 部分网站测试失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    success = test_final_config()
    
    if success:
        print(f"\n✅ 最终配置测试通过")
        print(f"\n📝 配置说明:")
        print(f"• 南方锰业网站: 使用southmn_site配置，解析成功")
        print(f"• 西矿电商平台: 使用westmining_bidding配置，解析成功")
        print(f"• 西矿集团官网: 不推荐使用，因为使用iframe嵌入")
        print(f"\n💡 建议:")
        print(f"• 使用推荐的URL配置获得最佳效果")
        print(f"• 如需监控西矿集团，直接使用电商平台URL")
        print(f"• 系统会自动去重，避免重复信息")
    else:
        print(f"\n❌ 配置需要调整")

if __name__ == '__main__':
    main()
