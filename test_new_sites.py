#!/usr/bin/env python3
"""
测试新网站配置
"""
import os
from src.config import Config
from src.scraper import WebScraper
from src.parser import HTMLParser, SELECTOR_CONFIGS
from src.formatter import MessageFormatter
from src.multi_site_configs import setup_multi_site_configs, get_config_for_url

def test_single_site(url, site_name):
    """测试单个网站"""
    print(f"\n🧪 测试网站: {site_name}")
    print(f"URL: {url}")
    print("-" * 50)
    
    try:
        # 获取配置
        config_name = get_config_for_url(url)
        print(f"自动选择配置: {config_name}")
        
        if config_name not in SELECTOR_CONFIGS:
            print(f"❌ 配置 '{config_name}' 不存在")
            return False
        
        config = SELECTOR_CONFIGS[config_name]
        print(f"配置详情: {config}")
        
        # 抓取网页
        scraper = WebScraper()
        html_content = scraper.fetch_page(url)
        
        if not html_content:
            print("❌ 网页抓取失败")
            scraper.close()
            return False
        
        print(f"✅ 网页抓取成功，内容长度: {len(html_content)}")
        
        # 解析数据
        parser = HTMLParser()
        bidding_list = parser.extract_bidding_info(html_content, config)
        
        print(f"📊 解析结果: {len(bidding_list)} 条信息")
        
        if bidding_list:
            print(f"前3条信息:")
            for i, bidding in enumerate(bidding_list[:3], 1):
                print(f"  {i}. {bidding.title}")
                print(f"     日期: {bidding.publish_date}")
                print(f"     链接: {bidding.link}")
                print()
            
            # 测试今日数据过滤
            today_bidding = parser.filter_today_data(bidding_list)
            print(f"📅 今日数据: {len(today_bidding)} 条")
            
            # 测试消息格式化
            formatter = MessageFormatter()
            if today_bidding:
                message = formatter.format_bidding_list_markdown(today_bidding, f"{site_name}今日招标信息")
            else:
                message = formatter.format_bidding_list_markdown(bidding_list[:3], f"{site_name}最新招标信息")
            
            print(f"💬 消息格式化完成，长度: {len(message)} 字符")
            
            # 保存消息到文件
            filename = f"test_message_{site_name.replace(' ', '_')}.md"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(message)
            print(f"💾 消息已保存到: {filename}")
            
            scraper.close()
            print(f"✅ {site_name} 测试成功")
            return True
        else:
            print(f"❌ 未解析到任何信息")
            scraper.close()
            return False
            
    except Exception as e:
        print(f"❌ 测试 {site_name} 时发生错误: {e}")
        return False

def test_multi_site_advanced():
    """测试高级多网站模式"""
    print(f"\n🚀 测试高级多网站模式")
    print("=" * 60)
    
    # 设置测试URL
    test_urls = [
        "http://mn.southmn.com/list/?368_1.html",
        "https://www.westmininggroup.com/tzgg/zbgg1/",
    ]
    
    # 临时设置环境变量
    os.environ['TARGET_URLS'] = ','.join(test_urls)
    Config.TARGET_URLS = test_urls
    
    try:
        from src.advanced_scheduler import AdvancedBiddingBot
        
        # 创建高级机器人实例（不需要企业微信配置）
        bot = AdvancedBiddingBot()
        
        print(f"配置的URL数量: {len(Config.get_target_urls())}")
        
        # 显示配置映射
        for url in Config.get_target_urls():
            config_name = bot._get_config_for_url(url)
            print(f"  {url} → {config_name}")
        
        # 测试抓取
        print(f"\n📡 开始测试抓取...")
        
        target_urls = Config.get_target_urls()
        all_bidding_list = []
        
        for i, url in enumerate(target_urls, 1):
            print(f"\n抓取第 {i}/{len(target_urls)} 个网页:")
            print(f"URL: {url}")
            
            config_name = bot._get_config_for_url(url)
            print(f"使用配置: {config_name}")
            
            html_content = bot.scraper.fetch_page(url)
            if html_content:
                config = SELECTOR_CONFIGS.get(config_name, SELECTOR_CONFIGS['default'])
                bidding_list = bot.parser.extract_bidding_info(html_content, config)
                print(f"✅ 解析到 {len(bidding_list)} 条信息")
                
                if bidding_list:
                    print(f"前2条:")
                    for j, bidding in enumerate(bidding_list[:2], 1):
                        print(f"  {j}. {bidding.title} ({bidding.publish_date})")
                
                all_bidding_list.extend(bidding_list)
            else:
                print(f"❌ 抓取失败")
        
        print(f"\n📊 总计解析到 {len(all_bidding_list)} 条信息")
        
        # 去重
        unique_list = bot._remove_duplicates(all_bidding_list)
        print(f"📋 去重后剩余 {len(unique_list)} 条信息")
        
        # 今日数据
        today_bidding = bot.parser.filter_today_data(unique_list)
        print(f"📅 今日数据: {len(today_bidding)} 条")
        
        # 格式化消息
        if today_bidding:
            message = bot.formatter.format_bidding_list_markdown(
                today_bidding, 
                "多网站今日招标信息汇总"
            )
        else:
            message = bot.formatter.format_bidding_list_markdown(
                unique_list[:5], 
                "多网站最新招标信息汇总"
            )
        
        print(f"💬 消息格式化完成，长度: {len(message)} 字符")
        
        # 保存消息
        with open('test_multi_site_message.md', 'w', encoding='utf-8') as f:
            f.write(message)
        print(f"💾 消息已保存到: test_multi_site_message.md")
        
        bot.scraper.close()
        
        print(f"\n✅ 高级多网站模式测试成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ 高级多网站模式测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试新网站配置")
    
    # 设置配置
    setup_multi_site_configs()
    
    # 测试单个网站
    sites = [
        ("http://mn.southmn.com/list/?368_1.html", "南方锰业"),
        ("https://www.westmininggroup.com/tzgg/zbgg1/", "西矿集团官网"),
    ]
    
    results = {}
    
    for url, name in sites:
        success = test_single_site(url, name)
        results[name] = success
    
    # 测试多网站模式
    multi_success = test_multi_site_advanced()
    
    # 总结
    print(f"\n" + "="*60)
    print(f"📊 测试总结")
    print(f"="*60)
    
    for name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {name}: {status}")
    
    multi_status = "✅ 成功" if multi_success else "❌ 失败"
    print(f"  多网站模式: {multi_status}")
    
    all_success = all(results.values()) and multi_success
    
    if all_success:
        print(f"\n🎉 所有测试通过！")
        print(f"\n📋 使用方法:")
        print(f"1. 配置多个URL到 .env 文件:")
        print(f"   TARGET_URLS=http://mn.southmn.com/list/?368_1.html,https://www.westmininggroup.com/tzgg/zbgg1/")
        print(f"2. 运行: python main_multi_url.py --test --mode advanced")
        print(f"3. 启动: python main_multi_url.py --mode advanced")
    else:
        print(f"\n❌ 部分测试失败，请检查配置")

if __name__ == '__main__':
    main()
