"""
消息格式化模块测试
"""
import unittest
from src.formatter import MessageFormatter
from src.parser import BiddingInfo


class TestMessageFormatter(unittest.TestCase):
    """消息格式化器测试"""
    
    def setUp(self):
        self.formatter = MessageFormatter()
        
        # 创建测试数据
        self.test_bidding_list = [
            BiddingInfo(
                title="测试招标项目1",
                link="https://example.com/1",
                publish_date="2024-01-15",
                content="这是一个测试项目的详细描述",
                category="工程建设",
                location="北京市"
            ),
            BiddingInfo(
                title="测试招标项目2",
                link="https://example.com/2",
                publish_date="2024-01-15",
                content="另一个测试项目",
                category="货物采购",
                location="上海市"
            )
        ]
    
    def test_format_bidding_list_markdown(self):
        """测试Markdown格式化"""
        result = self.formatter.format_bidding_list_markdown(self.test_bidding_list)
        
        # 检查基本结构
        self.assertIn("# 📋", result)  # 标题
        self.assertIn("📊 **信息数量**: 2 条", result)  # 统计信息
        self.assertIn("## 1. 测试招标项目1", result)  # 第一条信息
        self.assertIn("## 2. 测试招标项目2", result)  # 第二条信息
        self.assertIn("🤖", result)  # 底部信息
        
        # 检查具体内容
        self.assertIn("📅 **发布时间**: 2024-01-15", result)
        self.assertIn("🏷️ **分类**: 工程建设", result)
        self.assertIn("📍 **地区**: 北京市", result)
        self.assertIn("🔗 **详情链接**: [点击查看](https://example.com/1)", result)
    
    def test_format_bidding_list_text(self):
        """测试文本格式化"""
        result = self.formatter.format_bidding_list_text(self.test_bidding_list)
        
        # 检查基本结构
        self.assertIn("📋", result)  # 标题
        self.assertIn("📊 信息数量: 2 条", result)  # 统计信息
        self.assertIn("1. 测试招标项目1", result)  # 第一条信息
        self.assertIn("2. 测试招标项目2", result)  # 第二条信息
        
        # 检查具体内容
        self.assertIn("发布时间: 2024-01-15", result)
        self.assertIn("分类: 工程建设", result)
        self.assertIn("地区: 北京市", result)
        self.assertIn("链接: https://example.com/1", result)
    
    def test_format_empty_message(self):
        """测试空消息格式化"""
        result = self.formatter.format_bidding_list_markdown([])
        
        self.assertIn("📊 **信息数量**: 0 条", result)
        self.assertIn("🔍 今日暂无新的招标信息", result)
    
    def test_format_news_articles(self):
        """测试图文消息格式化"""
        articles = self.formatter.format_news_articles(self.test_bidding_list)
        
        self.assertEqual(len(articles), 2)
        
        # 检查第一条图文消息
        first_article = articles[0]
        self.assertEqual(first_article['title'], "测试招标项目1")
        self.assertEqual(first_article['url'], "https://example.com/1")
        self.assertIn("发布时间: 2024-01-15", first_article['description'])
        self.assertIn("分类: 工程建设", first_article['description'])
        self.assertIn("地区: 北京市", first_article['description'])
    
    def test_single_bidding_markdown_format(self):
        """测试单条招标信息Markdown格式化"""
        bidding = self.test_bidding_list[0]
        result = self.formatter._format_single_bidding_markdown(bidding, 1)
        
        self.assertIn("## 1. 测试招标项目1", result)
        self.assertIn("📅 **发布时间**: 2024-01-15", result)
        self.assertIn("🏷️ **分类**: 工程建设", result)
        self.assertIn("📍 **地区**: 北京市", result)
        self.assertIn("📝 **摘要**: 这是一个测试项目的详细描述", result)
        self.assertIn("🔗 **详情链接**: [点击查看](https://example.com/1)", result)
    
    def test_single_bidding_text_format(self):
        """测试单条招标信息文本格式化"""
        bidding = self.test_bidding_list[0]
        result = self.formatter._format_single_bidding_text(bidding, 1)
        
        self.assertIn("1. 测试招标项目1", result)
        self.assertIn("   发布时间: 2024-01-15", result)
        self.assertIn("   分类: 工程建设", result)
        self.assertIn("   地区: 北京市", result)
        self.assertIn("   链接: https://example.com/1", result)
    
    def test_message_length_limit(self):
        """测试消息长度限制"""
        # 创建大量测试数据
        large_bidding_list = []
        for i in range(50):
            bidding = BiddingInfo(
                title=f"测试招标项目{i}" * 10,  # 长标题
                link=f"https://example.com/{i}",
                publish_date="2024-01-15",
                content="这是一个非常长的项目描述" * 20,  # 长内容
                category="工程建设",
                location="北京市"
            )
            large_bidding_list.append(bidding)
        
        result = self.formatter.format_bidding_list_markdown(large_bidding_list)
        
        # 检查消息长度不超过限制
        self.assertLessEqual(len(result), self.formatter.max_message_length)
        
        # 检查是否包含截断提示
        if len(large_bidding_list) > 10:  # 如果数据量大，应该会被截断
            self.assertIn("还有", result)


if __name__ == '__main__':
    unittest.main()
