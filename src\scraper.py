"""
网站数据抓取模块
"""
import time
import random
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from loguru import logger
from .config import Config


class WebScraper:
    """网站数据抓取器"""
    
    def __init__(self):
        self.session = self._create_session()
        self.headers = self._get_headers()
    
    def _create_session(self):
        """创建带重试机制的session"""
        session = requests.Session()
        
        # 设置重试策略
        retry_strategy = Retry(
            total=Config.MAX_RETRIES,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def _get_headers(self):
        """获取请求头，模拟真实浏览器"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        return headers
    
    def fetch_page(self, url, params=None):
        """
        获取网页内容
        
        Args:
            url (str): 目标URL
            params (dict): 请求参数
            
        Returns:
            str: 网页HTML内容，失败返回None
        """
        try:
            logger.info(f"开始抓取页面: {url}")
            
            # 随机延时，避免被反爬
            delay = Config.REQUEST_DELAY + random.uniform(0, 2)
            time.sleep(delay)
            
            response = self.session.get(
                url,
                headers=self.headers,
                params=params,
                timeout=30
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 尝试检测编码
            if response.encoding == 'ISO-8859-1':
                response.encoding = response.apparent_encoding
            
            logger.info(f"成功获取页面内容，长度: {len(response.text)}")
            return response.text
            
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败: {e}")
            return None
        except Exception as e:
            logger.error(f"获取页面时发生未知错误: {e}")
            return None
    
    def fetch_multiple_pages(self, base_url, page_params_list):
        """
        批量获取多个页面
        
        Args:
            base_url (str): 基础URL
            page_params_list (list): 页面参数列表
            
        Returns:
            list: 页面内容列表
        """
        results = []
        
        for i, params in enumerate(page_params_list, 1):
            logger.info(f"正在获取第 {i}/{len(page_params_list)} 页")
            
            html_content = self.fetch_page(base_url, params)
            if html_content:
                results.append(html_content)
            else:
                logger.warning(f"第 {i} 页获取失败")
            
            # 页面间延时
            if i < len(page_params_list):
                time.sleep(Config.REQUEST_DELAY)
        
        logger.info(f"批量获取完成，成功获取 {len(results)}/{len(page_params_list)} 页")
        return results
    
    def close(self):
        """关闭session"""
        if self.session:
            self.session.close()
            logger.info("网络会话已关闭")


class AntiCrawlHandler:
    """反爬机制处理器"""
    
    @staticmethod
    def add_random_delay(min_delay=1, max_delay=3):
        """添加随机延时"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
        logger.debug(f"随机延时: {delay:.2f}秒")
    
    @staticmethod
    def rotate_user_agent():
        """轮换User-Agent"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        return random.choice(user_agents)
    
    @staticmethod
    def check_blocked(html_content):
        """检查是否被反爬拦截"""
        if not html_content:
            return True
        
        # 常见的反爬拦截关键词
        block_keywords = [
            '访问被拒绝',
            '请求过于频繁',
            '验证码',
            'captcha',
            'blocked',
            '403 Forbidden',
            '您的访问过于频繁'
        ]
        
        content_lower = html_content.lower()
        for keyword in block_keywords:
            if keyword.lower() in content_lower:
                logger.warning(f"检测到反爬拦截关键词: {keyword}")
                return True
        
        return False
