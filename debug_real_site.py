#!/usr/bin/env python3
"""
调试实际网站的HTML结构
"""
from src.scraper import WebScraper
from src.parser import HTMLParser, SELECTOR_CONFIGS
from bs4 import BeautifulSoup

def debug_real_site():
    """调试实际网站"""
    url = "https://ec.westmining.com/cms/channel/1ywgg1/index.htm"
    
    print(f"开始调试网站: {url}")
    
    # 1. 抓取网页
    scraper = WebScraper()
    html_content = scraper.fetch_page(url)
    
    if not html_content:
        print("❌ 网页抓取失败")
        return
    
    print(f"✅ 网页抓取成功，内容长度: {len(html_content)}")
    
    # 2. 解析HTML
    soup = BeautifulSoup(html_content, 'lxml')
    
    # 3. 查找可能的招标信息容器
    print("\n🔍 查找可能的招标信息容器...")
    
    # 检查各种可能的选择器
    selectors_to_test = [
        'li[name="li_name"]',  # 原配置
        'li',                  # 所有li元素
        '.li_name',           # class为li_name
        '#list1 li',          # list1下的li
        'ul li',              # ul下的li
        'tr',                 # 表格行
        '.infolist li',       # infolist下的li
        '.bidlist li',        # bidlist下的li
    ]
    
    for selector in selectors_to_test:
        elements = soup.select(selector)
        print(f"  {selector}: 找到 {len(elements)} 个元素")
        
        if len(elements) > 0 and len(elements) < 50:  # 合理数量的元素
            print(f"    前3个元素的内容预览:")
            for i, elem in enumerate(elements[:3]):
                # 查找链接
                link = elem.select_one('a')
                if link:
                    title = link.get('title', link.get_text(strip=True)[:50])
                    href = link.get('href', '')
                    print(f"      [{i+1}] 标题: {title}")
                    print(f"          链接: {href}")
                else:
                    text = elem.get_text(strip=True)[:100]
                    print(f"      [{i+1}] 文本: {text}")
    
    # 4. 检查特定的HTML结构
    print("\n🔍 检查特定HTML结构...")
    
    # 检查是否有list1
    list1 = soup.select_one('#list1')
    if list1:
        print("  ✅ 找到 #list1 元素")
        li_elements = list1.select('li')
        print(f"  #list1 下有 {len(li_elements)} 个li元素")
        
        if li_elements:
            print("  前3个li元素的详细结构:")
            for i, li in enumerate(li_elements[:3]):
                print(f"    Li[{i+1}]:")
                print(f"      name属性: {li.get('name', 'None')}")
                
                # 查找a标签
                a_tag = li.select_one('a')
                if a_tag:
                    print(f"      a标签 title: {a_tag.get('title', 'None')}")
                    print(f"      a标签 href: {a_tag.get('href', 'None')}")
                    
                    # 查找em标签
                    em_tags = a_tag.select('em')
                    print(f"      a标签内有 {len(em_tags)} 个em元素:")
                    for j, em in enumerate(em_tags):
                        em_text = em.get_text(strip=True)
                        print(f"        em[{j}]: '{em_text}'")
                else:
                    print("      未找到a标签")
                print()
    else:
        print("  ❌ 未找到 #list1 元素")
    
    # 5. 检查infolist结构
    infolist = soup.select_one('.infolist')
    if infolist:
        print("  ✅ 找到 .infolist 元素")
        li_elements = infolist.select('li')
        print(f"  .infolist 下有 {len(li_elements)} 个li元素")
    else:
        print("  ❌ 未找到 .infolist 元素")
    
    # 6. 保存HTML到文件用于分析
    with open('debug_html.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    print(f"\n💾 HTML内容已保存到 debug_html.html 文件")
    
    scraper.close()

if __name__ == '__main__':
    debug_real_site()
