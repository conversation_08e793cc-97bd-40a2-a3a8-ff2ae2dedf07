"""
自定义网站配置示例
"""

# 示例：中国政府采购网配置
CCGP_CONFIG = {
    'item_selector': '.vT-srch-result-list-bid .vT-srch-result-list-bid-gg',
    'title_selector': 'a',
    'date_selector': '.vT-srch-result-list-bid-gg-date',
    'category_selector': '.vT-srch-result-list-bid-gg-type',
    'location_selector': '.vT-srch-result-list-bid-gg-area',
    'base_url': 'http://www.ccgp.gov.cn'
}

# 示例：某省公共资源交易中心配置
PROVINCE_CONFIG = {
    'item_selector': '.ewb-list-node',
    'title_selector': '.ewb-list-node-title a',
    'date_selector': '.ewb-list-node-time',
    'category_selector': '.ewb-list-node-type',
    'location_selector': '.ewb-list-node-area',
    'content_selector': '.ewb-list-node-summary',
    'base_url': 'https://example-province.gov.cn'
}

# 示例：建设工程招标网配置
CONSTRUCTION_CONFIG = {
    'item_selector': 'tr[bgcolor]',
    'title_selector': 'td:nth-child(2) a',
    'date_selector': 'td:nth-child(4)',
    'location_selector': 'td:nth-child(3)',
    'base_url': 'https://construction-bidding.com'
}

# 将配置添加到系统中的方法
def add_custom_configs():
    """将自定义配置添加到系统中"""
    from src.parser import SELECTOR_CONFIGS
    
    SELECTOR_CONFIGS.update({
        'ccgp': CCGP_CONFIG,
        'province': PROVINCE_CONFIG,
        'construction': CONSTRUCTION_CONFIG
    })
    
    print("自定义配置已添加")

if __name__ == '__main__':
    add_custom_configs()
    print("可用配置:", list(SELECTOR_CONFIGS.keys()))
