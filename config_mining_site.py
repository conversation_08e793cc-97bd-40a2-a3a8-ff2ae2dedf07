#!/usr/bin/env python3
"""
矿业招标网站专用配置
根据您提供的HTML结构定制的配置文件
"""

# 矿业招标网站的选择器配置
MINING_SITE_CONFIG = {
    'item_selector': 'li[name="li_name"]',      # 招标项目容器
    'title_selector': 'a',                      # 标题链接选择器
    'date_selector': 'a em:nth-of-type(2)',     # 日期选择器（第二个em元素）
    'base_url': 'https://ec.westmining.com'  # 网站根域名
}

def setup_mining_site_config():
    """将矿业网站配置添加到系统中"""
    from src.parser import SELECTOR_CONFIGS
    
    SELECTOR_CONFIGS['mining_site'] = MINING_SITE_CONFIG
    print("矿业网站配置已添加到系统")

def test_with_mining_config():
    """使用矿业网站配置进行测试"""
    from src.scheduler import BiddingBot
    
    # 添加配置
    setup_mining_site_config()
    
    # 创建机器人实例
    bot = BiddingBot('mining_site')
    
    # 运行测试
    print("开始测试矿业网站配置...")
    success = bot.test_all_components()
    
    if success:
        print("✅ 矿业网站配置测试成功！")
    else:
        print("❌ 矿业网站配置测试失败！")
    
    return success

if __name__ == '__main__':
    # 添加配置
    setup_mining_site_config()
    
    # 显示配置信息
    print("矿业招标网站配置:")
    print("=" * 40)
    for key, value in MINING_SITE_CONFIG.items():
        print(f"{key}: {value}")
    print("=" * 40)
    
    print("\n使用方法:")
    print("1. 修改 config.env 文件中的 TARGET_URL 为实际的矿业招标网站URL")
    print("2. 运行: python main.py --config mining_site --test")
    print("3. 测试成功后运行: python main.py --config mining_site --once")
    print("4. 启动定时任务: python main.py --config mining_site")
