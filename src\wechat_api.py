"""
企业微信API模块
"""
import json
import time
import requests
from datetime import datetime, timedelta
from loguru import logger
from .config import Config


class WeChatAPI:
    """企业微信API客户端"""
    
    def __init__(self):
        self.corp_id = Config.CORP_ID
        self.corp_secret = Config.CORP_SECRET
        self.agent_id = Config.AGENT_ID
        self.access_token = None
        self.token_expires_at = None
        self.base_url = "https://qyapi.weixin.qq.com/cgi-bin"
    
    def get_access_token(self) -> str:
        """
        获取访问令牌
        
        Returns:
            str: access_token
        """
        # 检查token是否有效
        if self._is_token_valid():
            logger.debug("使用缓存的access_token")
            return self.access_token
        
        # 获取新的token
        url = f"{self.base_url}/gettoken"
        params = {
            'corpid': self.corp_id,
            'corpsecret': self.corp_secret
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('errcode') == 0:
                self.access_token = data.get('access_token')
                expires_in = data.get('expires_in', 7200)
                
                # 设置过期时间，提前5分钟刷新
                self.token_expires_at = datetime.now() + timedelta(seconds=expires_in - 300)
                
                logger.info("成功获取access_token")
                return self.access_token
            else:
                error_msg = f"获取access_token失败: {data.get('errmsg', '未知错误')}"
                logger.error(error_msg)
                raise Exception(error_msg)
                
        except requests.exceptions.RequestException as e:
            logger.error(f"请求access_token时网络错误: {e}")
            raise
        except Exception as e:
            logger.error(f"获取access_token时发生错误: {e}")
            raise
    
    def _is_token_valid(self) -> bool:
        """检查token是否有效"""
        if not self.access_token or not self.token_expires_at:
            return False
        
        return datetime.now() < self.token_expires_at
    
    def send_text_message(self, content: str, to_user: str = None, 
                         to_party: str = None, to_tag: str = None) -> bool:
        """
        发送文本消息
        
        Args:
            content (str): 消息内容
            to_user (str): 接收用户，多个用|分隔
            to_party (str): 接收部门，多个用|分隔
            to_tag (str): 接收标签，多个用|分隔
            
        Returns:
            bool: 发送是否成功
        """
        try:
            access_token = self.get_access_token()
            url = f"{self.base_url}/message/send?access_token={access_token}"
            
            # 构建消息数据
            data = {
                "touser": to_user or Config.TO_USER,
                "toparty": to_party or Config.TO_PARTY,
                "totag": to_tag or Config.TO_TAG,
                "msgtype": "text",
                "agentid": self.agent_id,
                "text": {
                    "content": content
                }
            }
            
            response = requests.post(
                url, 
                json=data, 
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('errcode') == 0:
                logger.info("消息发送成功")
                return True
            else:
                error_msg = f"消息发送失败: {result.get('errmsg', '未知错误')}"
                logger.error(error_msg)
                return False
                
        except Exception as e:
            logger.error(f"发送消息时发生错误: {e}")
            return False
    
    def send_markdown_message(self, content: str, to_user: str = None,
                            to_party: str = None, to_tag: str = None) -> bool:
        """
        发送Markdown消息
        
        Args:
            content (str): Markdown格式的消息内容
            to_user (str): 接收用户
            to_party (str): 接收部门
            to_tag (str): 接收标签
            
        Returns:
            bool: 发送是否成功
        """
        try:
            access_token = self.get_access_token()
            url = f"{self.base_url}/message/send?access_token={access_token}"
            
            data = {
                "touser": to_user or Config.TO_USER,
                "toparty": to_party or Config.TO_PARTY,
                "totag": to_tag or Config.TO_TAG,
                "msgtype": "markdown",
                "agentid": self.agent_id,
                "markdown": {
                    "content": content
                }
            }
            
            response = requests.post(
                url,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('errcode') == 0:
                logger.info("Markdown消息发送成功")
                return True
            else:
                error_msg = f"Markdown消息发送失败: {result.get('errmsg', '未知错误')}"
                logger.error(error_msg)
                return False
                
        except Exception as e:
            logger.error(f"发送Markdown消息时发生错误: {e}")
            return False
    
    def send_news_message(self, articles: list, to_user: str = None,
                         to_party: str = None, to_tag: str = None) -> bool:
        """
        发送图文消息
        
        Args:
            articles (list): 图文消息列表，每个元素包含title, description, url, picurl
            to_user (str): 接收用户
            to_party (str): 接收部门
            to_tag (str): 接收标签
            
        Returns:
            bool: 发送是否成功
        """
        try:
            access_token = self.get_access_token()
            url = f"{self.base_url}/message/send?access_token={access_token}"
            
            data = {
                "touser": to_user or Config.TO_USER,
                "toparty": to_party or Config.TO_PARTY,
                "totag": to_tag or Config.TO_TAG,
                "msgtype": "news",
                "agentid": self.agent_id,
                "news": {
                    "articles": articles
                }
            }
            
            response = requests.post(
                url,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            response.raise_for_status()
            
            result = response.json()
            
            if result.get('errcode') == 0:
                logger.info("图文消息发送成功")
                return True
            else:
                error_msg = f"图文消息发送失败: {result.get('errmsg', '未知错误')}"
                logger.error(error_msg)
                return False
                
        except Exception as e:
            logger.error(f"发送图文消息时发生错误: {e}")
            return False
    
    def test_connection(self) -> bool:
        """测试企业微信连接"""
        try:
            access_token = self.get_access_token()
            if access_token:
                logger.info("企业微信连接测试成功")
                return True
            else:
                logger.error("企业微信连接测试失败")
                return False
        except Exception as e:
            logger.error(f"企业微信连接测试失败: {e}")
            return False
