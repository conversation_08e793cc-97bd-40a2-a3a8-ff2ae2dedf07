#!/usr/bin/env python3
"""
AutoWebsite - 多URL招标信息自动抓取推送系统
支持多个网页和多种配置的主程序入口
"""

import sys
import argparse
from src.multi_url_scheduler import MultiUrlBiddingBot
from src.advanced_scheduler import AdvancedBiddingBot


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='多URL招标信息自动抓取推送系统')
    parser.add_argument('--test', action='store_true', help='运行测试模式')
    parser.add_argument('--once', action='store_true', help='执行一次任务')
    parser.add_argument('--mode', choices=['simple', 'advanced'], default='simple', 
                       help='运行模式: simple(简单多URL) 或 advanced(高级多配置)')
    
    args = parser.parse_args()
    
    try:
        if args.mode == 'advanced':
            # 高级模式：支持不同URL使用不同配置
            bot = AdvancedBiddingBot()
            print("🚀 启动高级多URL多配置模式")
        else:
            # 简单模式：所有URL使用相同配置
            bot = MultiUrlBiddingBot('mining_site')
            print("🚀 启动简单多URL模式")
        
        if args.test:
            # 测试模式
            print("🧪 运行测试模式...")
            success = bot.test_all_components()
            sys.exit(0 if success else 1)
        elif args.once:
            # 执行一次
            print("▶️ 执行一次任务...")
            success = bot.run_once()
            sys.exit(0 if success else 1)
        else:
            # 定时任务模式
            print("⏰ 启动定时任务模式...")
            bot.start_scheduler()
            
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序运行时发生错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
