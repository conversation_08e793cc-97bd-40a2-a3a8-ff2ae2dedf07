"""
配置管理模块
"""
import os
from dotenv import load_dotenv
from loguru import logger

# 加载环境变量
load_dotenv()

class Config:
    """配置类"""
    
    # 企业微信配置
    CORP_ID = os.getenv('CORP_ID')
    CORP_SECRET = os.getenv('CORP_SECRET')
    AGENT_ID = os.getenv('AGENT_ID')
    
    # 招标网站配置
    TARGET_URL = os.getenv('TARGET_URL')
    TARGET_URLS = os.getenv('TARGET_URLS', '').split(',') if os.getenv('TARGET_URLS') else []
    REQUEST_DELAY = int(os.getenv('REQUEST_DELAY', 2))
    MAX_RETRIES = int(os.getenv('MAX_RETRIES', 3))
    
    # 消息推送配置
    TO_USER = os.getenv('TO_USER', '@all')
    TO_PARTY = os.getenv('TO_PARTY', '')
    TO_TAG = os.getenv('TO_TAG', '')
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'logs/app.log')
    
    # 定时任务配置
    SCHEDULE_TIME = os.getenv('SCHEDULE_TIME', '09:00')
    
    @classmethod
    def get_target_urls(cls):
        """获取目标URL列表"""
        urls = []

        # 单个URL配置
        if cls.TARGET_URL:
            urls.append(cls.TARGET_URL)

        # 多个URL配置
        if cls.TARGET_URLS:
            for url in cls.TARGET_URLS:
                url = url.strip()
                if url and url not in urls:
                    urls.append(url)

        return urls

    @classmethod
    def validate(cls):
        """验证必要的配置项"""
        required_fields = ['CORP_ID', 'CORP_SECRET', 'AGENT_ID']
        missing_fields = []

        for field in required_fields:
            if not getattr(cls, field):
                missing_fields.append(field)

        # 检查是否至少有一个URL配置
        urls = cls.get_target_urls()
        if not urls:
            missing_fields.append('TARGET_URL or TARGET_URLS')

        if missing_fields:
            logger.error(f"缺少必要的配置项: {', '.join(missing_fields)}")
            raise ValueError(f"缺少必要的配置项: {', '.join(missing_fields)}")

        logger.info(f"配置验证通过，找到 {len(urls)} 个目标URL")
        return True

# 初始化日志
def setup_logging():
    """设置日志配置"""
    # 创建logs目录
    os.makedirs('logs', exist_ok=True)
    
    # 配置loguru
    logger.remove()  # 移除默认处理器
    logger.add(
        Config.LOG_FILE,
        rotation="1 day",
        retention="30 days",
        level=Config.LOG_LEVEL,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    )
    logger.add(
        lambda msg: print(msg, end=""),
        level=Config.LOG_LEVEL,
        format="{time:HH:mm:ss} | {level} | {message}"
    )
